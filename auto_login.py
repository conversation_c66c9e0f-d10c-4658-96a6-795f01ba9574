"""
Tantra Bot - Auto Login Module
Handles automated login sequence with server and character selection.
"""

import time
import logging
import subprocess
from typing import Optional, Dict, Any
from game_interface import GameInterface

class AutoLogin:
    """Manages automated login process for <PERSON><PERSON>."""

    def __init__(self, game_interface: GameInterface):
        self.game = game_interface
        self.logger = logging.getLogger(__name__)

        # Configuration
        self.config = {
            'username': '',
            'password': '',
            'server': '<PERSON><PERSON>',  # <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Warzone
            'character': 'Character1',  # Character1, Character2, Character3
            'delays': {
                'after_enter': 500,
                'after_update': 3000,
                'after_launcher': 5000,
                'after_login': 3000,
                'after_server': 3000,
                'after_character': 3000
            },
            'enable_auto_login': False,
            'auto_login_delay': 0
        }

        # Server coordinates
        self.server_coords = {
            'Manas': 260,
            '<PERSON>yana': 295,
            '<PERSON><PERSON>': 328,
            '<PERSON>adi': 362,
            'Warzone': 395
        }

        # Character coordinates
        self.character_coords = {
            'Character1': {
                'select': (278, 484),
                'enter': (172, 428)
            },
            'Character2': {
                'select': (514, 484),
                'enter': (172, 428)
            },
            'Character3': {
                'select': (750, 484),
                'enter': (172, 429)
            }
        }

    def wait_for_window(self, window_title: str, timeout: int = 30) -> bool:
        """Wait for a specific window to appear."""
        start_time = time.time()

        while time.time() - start_time < timeout:
            if self.game.find_game_window(window_title):
                return True
            time.sleep(1)

        return False

    def handle_update_process(self) -> bool:
        """Handle the update process if it appears."""
        try:
            # Check if update window exists
            if self.wait_for_window("Update", 5):
                self.logger.info("Update window detected, handling...")

                # Send ENTER to handle update dialogs
                self.game.send_key('ENTER')
                time.sleep(500)

                # Keep sending ENTER until update window closes
                while self.game.find_game_window("Update"):
                    time.sleep(500)
                    self.game.send_key('ENTER')
                    time.sleep(500)
                    self.game.send_key('ENTER')

                time.sleep(self.config['delays']['after_update'])
                return True

        except Exception as e:
            self.logger.error(f"Error handling update process: {e}")

        return False

    def perform_login_sequence(self) -> bool:
        """Perform the complete login sequence."""
        if not self.config['enable_auto_login']:
            return False

        if not self.config['username'] or not self.config['password']:
            self.logger.error("Username or password not configured")
            return False

        try:
            self.logger.info("Starting auto-login sequence")

            # Wait for initial delay
            if self.config['auto_login_delay'] > 0:
                time.sleep(self.config['auto_login_delay'] / 1000.0)

            # Handle update process
            self.handle_update_process()

            # Wait for Tantra Launcher
            if not self.wait_for_window("Tantra Launcher", 30):
                self.logger.error("Tantra Launcher window not found")
                return False

            # Rename window to our configured title
            self.game.window_title = "Tantra Launcher"  # Keep original for login
            time.sleep(self.config['delays']['after_launcher'])

            # Click on username field and enter credentials
            self.game.click_at_position(520, 440)

            # Clear and enter username
            self.game.send_key_combination(['CTRL', 'a'])  # Select all
            self.game.send_key('BACKSPACE')
            self.game.send_text(self.config['username'])

            # Tab to password field
            self.game.send_key('TAB')

            # Clear and enter password
            self.game.send_key_combination(['CTRL', 'a'])  # Select all
            self.game.send_key('BACKSPACE')
            self.game.send_text(self.config['password'])

            # Submit login
            self.game.send_key('ENTER')
            time.sleep(self.config['delays']['after_login'])

            # Select server
            if not self._select_server():
                return False

            # Select character
            if not self._select_character():
                return False

            self.logger.info("Auto-login sequence completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error during login sequence: {e}")
            return False

    def _select_server(self) -> bool:
        """Select the configured server."""
        try:
            server_y = self.server_coords.get(self.config['server'])
            if not server_y:
                self.logger.error(f"Unknown server: {self.config['server']}")
                return False

            self.logger.info(f"Selecting server: {self.config['server']}")

            # Click on server multiple times to ensure selection
            for _ in range(4):
                self.game.click_at_position(478, server_y)
                time.sleep(50)

            # Submit server selection
            self.game.send_key('ENTER')
            time.sleep(self.config['delays']['after_server'])

            return True

        except Exception as e:
            self.logger.error(f"Error selecting server: {e}")
            return False

    def _select_character(self) -> bool:
        """Select the configured character."""
        try:
            char_coords = self.character_coords.get(self.config['character'])
            if not char_coords:
                self.logger.error(f"Unknown character: {self.config['character']}")
                return False

            self.logger.info(f"Selecting character: {self.config['character']}")

            # Click on character selection multiple times
            select_x, select_y = char_coords['select']
            for _ in range(3):
                self.game.click_at_position(select_x, select_y)
                time.sleep(500)

            # Submit character selection
            self.game.send_key('ENTER')
            time.sleep(self.config['delays']['after_character'])

            # Click enter game button multiple times
            enter_x, enter_y = char_coords['enter']
            for _ in range(8):
                self.game.click_at_position(enter_x, enter_y)
                time.sleep(500)

            return True

        except Exception as e:
            self.logger.error(f"Error selecting character: {e}")
            return False

    def check_disconnection(self) -> bool:
        """Check if the game has disconnected and needs re-login."""
        try:
            # Check if we can find the game window
            if not self.game.is_window_active():
                self.logger.warning("Game window not active, checking for disconnection")

                # Try to find Tantra Launcher (login screen)
                if self.game.find_game_window("Tantra Launcher"):
                    self.logger.info("Disconnection detected, attempting auto-login")
                    return self.perform_login_sequence()

        except Exception as e:
            self.logger.error(f"Error checking disconnection: {e}")

        return False

    def update_config(self, new_config: Dict[str, Any]):
        """Update auto-login configuration."""
        self.config.update(new_config)
        self.logger.info("Auto-login configuration updated")

    def get_login_status(self) -> Dict[str, Any]:
        """Get current login status and configuration."""
        return {
            'config': {
                'username': self.config['username'],
                'server': self.config['server'],
                'character': self.config['character'],
                'enable_auto_login': self.config['enable_auto_login'],
                'auto_login_delay': self.config['auto_login_delay']
            },
            'window_active': self.game.is_window_active(),
            'available_servers': list(self.server_coords.keys()),
            'available_characters': list(self.character_coords.keys())
        }