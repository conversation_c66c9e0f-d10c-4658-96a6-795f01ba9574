"""
Tantra Bot - Memory Management Module
Handles Windows process memory reading and writing operations.
"""

import ctypes
import ctypes.wintypes
from ctypes import wintypes, windll
import struct
import logging
from typing import Optional, Union, List

# Windows API constants
PROCESS_ALL_ACCESS = 0x1F0FFF
PROCESS_VM_READ = 0x0010
PROCESS_VM_WRITE = 0x0020
PROCESS_VM_OPERATION = 0x0008
PROCESS_QUERY_INFORMATION = 0x0400

# Memory protection constants
PAGE_EXECUTE_READWRITE = 0x40
PAGE_READWRITE = 0x04

class MemoryManager:
    """Handles memory operations for the Tantra game process."""

    def __init__(self):
        self.process_handle = None
        self.process_id = None
        self.logger = logging.getLogger(__name__)

        # Initialize Windows API functions
        self.kernel32 = windll.kernel32
        self.user32 = windll.user32
        self.psapi = windll.psapi

        # Set up function prototypes
        self._setup_api_prototypes()

    def _setup_api_prototypes(self):
        """Set up Windows API function prototypes."""
        # OpenProcess
        self.kernel32.OpenProcess.argtypes = [wintypes.DWORD, wintypes.BOOL, wintypes.DWORD]
        self.kernel32.OpenProcess.restype = wintypes.HANDLE

        # ReadProcessMemory
        self.kernel32.ReadProcessMemory.argtypes = [
            wintypes.HANDLE, wintypes.LPCVOID, wintypes.LPVOID,
            ctypes.c_size_t, ctypes.POINTER(ctypes.c_size_t)
        ]
        self.kernel32.ReadProcessMemory.restype = wintypes.BOOL

        # WriteProcessMemory
        self.kernel32.WriteProcessMemory.argtypes = [
            wintypes.HANDLE, wintypes.LPVOID, wintypes.LPCVOID,
            ctypes.c_size_t, ctypes.POINTER(ctypes.c_size_t)
        ]
        self.kernel32.WriteProcessMemory.restype = wintypes.BOOL

        # CloseHandle
        self.kernel32.CloseHandle.argtypes = [wintypes.HANDLE]
        self.kernel32.CloseHandle.restype = wintypes.BOOL

        # GetWindowThreadProcessId
        self.user32.GetWindowThreadProcessId.argtypes = [wintypes.HWND, ctypes.POINTER(wintypes.DWORD)]
        self.user32.GetWindowThreadProcessId.restype = wintypes.DWORD

    def attach_to_process(self, process_name: str = "HTLauncher.exe") -> bool:
        """
        Attach to the Tantra game process.

        Args:
            process_name: Name of the process to attach to

        Returns:
            bool: True if successfully attached, False otherwise
        """
        try:
            # Find process by name
            self.process_id = self._find_process_by_name(process_name)
            if not self.process_id:
                self.logger.error(f"Process {process_name} not found")
                return False

            # Open process with required permissions
            self.process_handle = self.kernel32.OpenProcess(
                PROCESS_ALL_ACCESS, False, self.process_id
            )

            if not self.process_handle:
                self.logger.error(f"Failed to open process {process_name}")
                return False

            self.logger.info(f"Successfully attached to {process_name} (PID: {self.process_id})")
            return True

        except Exception as e:
            self.logger.error(f"Error attaching to process: {e}")
            return False

    def _find_process_by_name(self, process_name: str) -> Optional[int]:
        """Find process ID by process name."""
        try:
            # Get list of process IDs
            process_ids = (wintypes.DWORD * 1024)()
            bytes_returned = wintypes.DWORD()

            if not self.psapi.EnumProcesses(
                ctypes.byref(process_ids),
                ctypes.sizeof(process_ids),
                ctypes.byref(bytes_returned)
            ):
                return None

            # Calculate number of processes
            num_processes = bytes_returned.value // ctypes.sizeof(wintypes.DWORD)

            # Check each process
            for i in range(num_processes):
                pid = process_ids[i]
                if pid == 0:
                    continue

                # Open process to get name
                handle = self.kernel32.OpenProcess(PROCESS_QUERY_INFORMATION, False, pid)
                if handle:
                    try:
                        # Get process name
                        name_buffer = ctypes.create_string_buffer(260)
                        if self.psapi.GetProcessImageFileNameA(handle, name_buffer, 260):
                            full_path = name_buffer.value.decode('utf-8', errors='ignore')
                            if process_name.lower() in full_path.lower():
                                self.kernel32.CloseHandle(handle)
                                return pid
                    finally:
                        self.kernel32.CloseHandle(handle)

            return None

        except Exception as e:
            self.logger.error(f"Error finding process: {e}")
            return None

    def read_memory(self, address: int, size: int) -> Optional[bytes]:
        """
        Read memory from the attached process.

        Args:
            address: Memory address to read from
            size: Number of bytes to read

        Returns:
            bytes: Read data or None if failed
        """
        if not self.process_handle:
            self.logger.error("No process attached")
            return None

        try:
            buffer = ctypes.create_string_buffer(size)
            bytes_read = ctypes.c_size_t()

            success = self.kernel32.ReadProcessMemory(
                self.process_handle,
                ctypes.c_void_p(address),
                buffer,
                size,
                ctypes.byref(bytes_read)
            )

            if success and bytes_read.value == size:
                return buffer.raw
            else:
                self.logger.warning(f"Failed to read memory at 0x{address:X}")
                return None

        except Exception as e:
            self.logger.error(f"Error reading memory: {e}")
            return None

    def write_memory(self, address: int, data: bytes) -> bool:
        """
        Write memory to the attached process.

        Args:
            address: Memory address to write to
            data: Data to write

        Returns:
            bool: True if successful, False otherwise
        """
        if not self.process_handle:
            self.logger.error("No process attached")
            return False

        try:
            bytes_written = ctypes.c_size_t()

            success = self.kernel32.WriteProcessMemory(
                self.process_handle,
                ctypes.c_void_p(address),
                data,
                len(data),
                ctypes.byref(bytes_written)
            )

            if success and bytes_written.value == len(data):
                return True
            else:
                self.logger.warning(f"Failed to write memory at 0x{address:X}")
                return False

        except Exception as e:
            self.logger.error(f"Error writing memory: {e}")
            return False

    def read_int32(self, address: int) -> Optional[int]:
        """Read a 32-bit integer from memory."""
        data = self.read_memory(address, 4)
        if data:
            return struct.unpack('<I', data)[0]
        return None

    def read_float(self, address: int) -> Optional[float]:
        """Read a float from memory."""
        data = self.read_memory(address, 4)
        if data:
            return struct.unpack('<f', data)[0]
        return None

    def read_string(self, address: int, max_length: int = 256) -> Optional[str]:
        """Read a null-terminated string from memory."""
        data = self.read_memory(address, max_length)
        if data:
            # Find null terminator
            null_pos = data.find(b'\x00')
            if null_pos != -1:
                data = data[:null_pos]
            try:
                return data.decode('utf-8', errors='ignore')
            except:
                return data.decode('latin-1', errors='ignore')
        return None

    def write_int32(self, address: int, value: int) -> bool:
        """Write a 32-bit integer to memory."""
        data = struct.pack('<I', value)
        return self.write_memory(address, data)

    def write_byte(self, address: int, value: int) -> bool:
        """Write a single byte to memory."""
        data = struct.pack('<B', value & 0xFF)
        return self.write_memory(address, data)

    def get_pointer_value(self, base_address: int) -> Optional[int]:
        """Get the value that a pointer points to."""
        pointer_data = self.read_memory(base_address, 4)
        if pointer_data:
            return struct.unpack('<I', pointer_data)[0]
        return None

    def read_with_offset(self, base_address: int, offset: int, size: int = 4) -> Optional[int]:
        """Read memory using base address + offset."""
        pointer_value = self.get_pointer_value(base_address)
        if pointer_value:
            target_address = pointer_value + offset
            if size == 4:
                return self.read_int32(target_address)
            elif size == 1:
                data = self.read_memory(target_address, 1)
                if data:
                    return struct.unpack('<B', data)[0]
        return None

    def close(self):
        """Close the process handle."""
        if self.process_handle:
            self.kernel32.CloseHandle(self.process_handle)
            self.process_handle = None
            self.process_id = None
            self.logger.info("Process handle closed")

    def __del__(self):
        """Destructor to ensure handle is closed."""
        self.close()