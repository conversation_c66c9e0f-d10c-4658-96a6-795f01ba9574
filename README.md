# 🔥 Tantra Bot - Epic Edition 🔥

A comprehensive Python rewrite of the Tantra Online MMORPG bot with modern architecture, epic GUI design, and all original features plus enhancements.

## ✨ Features

### 🎮 Core Bot Features
- **Automated Combat**: Target selection, skill rotation, auto-attack
- **Character Management**: HP/TP monitoring, healing, buffing, repair
- **Social Features**: PM auto-reply, party management, trade/duel blocking
- **Auto-Login**: Automated login sequence with server/character selection
- **Portal Hack**: Mandara teleport exploit functionality
- **Anti-Stuck**: Intelligent movement and rotation detection

### 🎨 Epic GUI Interface
- **Modern Design**: Dark theme with orange accents and dramatic effects
- **Real-time Status**: Live character and combat status monitoring
- **Configuration Tabs**: Organized settings for all bot features
- **Log Viewer**: Real-time bot activity logging with color coding
- **Quick Actions**: One-click healing, buffing, targeting, and repair

### ⌨️ Hotkey System
- **F8**: Start/Stop main bot
- **F9**: Trade spam mode
- **F10**: Emergency stop
- **F11**: Click spam mode
- **Shift+Alt+M**: Show main GUI
- **Shift+Alt+A**: Auto-login
- **Shift+Alt+T**: Portal hack
- **Alt******: Position capture

### 🔧 Advanced Features
- **Memory Management**: Direct game memory reading/writing
- **Configuration System**: JSON-based settings with save/load
- **Error Handling**: Comprehensive error recovery and logging
- **Multi-threading**: Separate threads for different bot functions
- **Rate Limiting**: Intelligent execution frequency control

## 📋 Requirements

- **Python 3.8+**
- **Windows OS** (for Windows API access)
- **Tantra Online** game client
- **Administrator privileges** (for memory access)

## 🚀 Installation

1. **Clone or download** this repository
2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
3. **Run as Administrator** (required for memory access)

## 🎯 Usage

### Quick Start
1. **Start Tantra Online** and log into your character
2. **Run the bot**:
   ```bash
   python tantra_bot.py
   ```
3. **Configure settings** in the GUI tabs
4. **Press F8** or click "START BOT" to begin automation

### Configuration

#### Character Settings
- **Heal Threshold**: HP percentage to trigger healing (default: 70%)
- **TP Threshold**: TP percentage to use potions (default: 70%)
- **Auto-Heal**: Enable automatic healing with skills
- **Auto-Buffs**: Enable automatic buff casting (F2 skills 1-4)
- **Auto-Repair**: Enable automatic equipment repair
- **Auto-Resurrection**: Enable Silfrijan auto-resurrection

#### Combat Settings
- **Target Monsters**: List of monster names to target
- **Skill Rotation**: F1 skills 1,2,4,3 in sequence
- **Auto-Attack**: Use R key for basic attacks
- **Looting**: Automatic item pickup with F key
- **Anti-Stuck**: Movement when character gets stuck

#### Social Features
- **PM Auto-Reply**: Respond to private messages
- **Party Management**: Auto-accept or decline party invitations
- **Trade Blocking**: Automatically decline trade requests
- **Duel Blocking**: Automatically decline duel requests

#### Auto-Login
- **Username/Password**: Game login credentials
- **Server Selection**: Choose from Manas, Diyana, Kriya, Samadi, Warzone
- **Character Selection**: Choose Character1, Character2, or Character3
- **Auto-Reconnect**: Detect disconnections and re-login

#### Portal Hack
- **Address Search**: Automatically find portal memory addresses
- **Teleport Exploit**: Mandara portal hack functionality
- **Multiple Destinations**: Pre-configured teleport locations

## 🎮 Game Memory Addresses

The bot uses these memory addresses for Tantra Online:

- **Character Base**: `0x109BE1D0`
- **Target Monster**: `0x109BE280`
- **Dialog Addresses**: Various for party/trade/duel detection
- **Chat Messages**: `0x109BE200` + offset
- **HP/TP/Damage**: Character base + specific offsets

## 🔒 Security & Safety

- **Memory Protection**: Safe memory reading/writing with error handling
- **Process Validation**: Verify game process before attachment
- **Rate Limiting**: Prevent excessive API calls
- **Error Recovery**: Graceful handling of game crashes/disconnections
- **Configuration Backup**: Automatic config file management

## 🐛 Troubleshooting

### Common Issues

1. **"Process not found"**
   - Ensure Tantra Online is running
   - Run bot as Administrator
   - Check process name (HTLauncher.exe)

2. **"Failed to attach to process"**
   - Close antivirus temporarily
   - Run both game and bot as Administrator
   - Check Windows permissions

3. **"Window not found"**
   - Ensure game window is visible
   - Check window title configuration
   - Try different window detection methods

4. **Memory read errors**
   - Game version mismatch
   - Update memory addresses
   - Check process architecture (32/64-bit)

### Debug Mode
Enable detailed logging by setting log level to DEBUG in the configuration.

## 📁 File Structure

```
tantra_bot/
├── tantra_bot.py          # Main bot controller
├── memory_manager.py      # Windows memory operations
├── game_interface.py      # Window/input control
├── character_manager.py   # Character status/healing
├── combat_system.py       # Combat and targeting
├── social_manager.py      # Social features
├── auto_login.py          # Login automation
├── portal_hack.py         # Portal exploit
├── config_manager.py      # Configuration system
├── gui_interface.py       # Epic GUI interface
├── utils.py              # Utility functions
├── requirements.txt       # Python dependencies
├── README.md             # This file
└── tantra_bot_config.json # Configuration file (auto-generated)
```

## ⚠️ Disclaimer

This bot is for educational purposes only. Use at your own risk. The authors are not responsible for:
- Account bans or suspensions
- Game client crashes or data loss
- Any violations of game terms of service
- System security issues

Always ensure you have permission to use automation tools and comply with the game's terms of service.

## 🤝 Contributing

Contributions are welcome! Please:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is provided as-is for educational purposes. Use responsibly and at your own risk.

## 🔥 Epic Features Coming Soon

- **AI-powered targeting** with machine learning
- **Advanced pathfinding** for complex navigation
- **Multi-character support** for party botting
- **Web dashboard** for remote monitoring
- **Plugin system** for custom extensions
- **Voice commands** for hands-free control

---

**Made with 🔥 by Epic Developers**

*"Automate like a legend, dominate like a god!"*