"""
<PERSON><PERSON> Bot - Configuration Manager <PERSON><PERSON><PERSON>
Handles save/load functionality for bot settings using JSON.
"""

import json
import os
import logging
from typing import Dict, Any, Optional
from datetime import datetime

class ConfigManager:
    """Manages bot configuration saving and loading."""

    def __init__(self, config_file: str = "tantra_bot_config.json"):
        self.config_file = config_file
        self.logger = logging.getLogger(__name__)

        # Default configuration
        self.default_config = {
            'version': '1.0',
            'last_updated': '',

            # Character settings
            'character': {
                'heal_threshold': 70,
                'tp_threshold': 70,
                'heal_skill_key': '7',
                'hp_pot_key': '8',
                'tp_pot_key': '9',
                'heal_delay': 2000,
                'hp_pot_delay': 2000,
                'tp_pot_delay': 2000,
                'enable_auto_heal': True,
                'enable_hp_pots': False,
                'enable_tp_pots': False,
                'enable_auto_silf': False,
                'enable_buffs': True,
                'enable_auto_repair': False,
                'auto_silf_coords': [150, 500],
                'buff_durations': {
                    'buff1': 480000,
                    'buff2': 600000,
                    'buff3': 300000,
                    'buff4': 300000,
                    'repair': 300000
                }
            },

            # Combat settings
            'combat': {
                'target_monsters': [
                    'ANTI AVARA CARA',
                    'Kemsa Tonyo',
                    'Mosa Tonyo'
                ],
                'enable_monster_select': True,
                'enable_skills': True,
                'enable_r_key': True,
                'enable_looting': True,
                'skill_delays': {
                    '1': 200,
                    '2': 200,
                    '3': 200,
                    '4': 200
                },
                'loot_delay': 200,
                'anti_stuck_spin_time': 150,
                'anti_stuck_move_time': 500,
                'delay_before_skill': 1000,
                'enable_delay_before_skill': True
            },

            # Social settings
            'social': {
                'enable_pm_reply': False,
                'pm_reply_text': 'Pil0t p0h',
                'enable_party_accept': True,
                'party_accept_reply': 'Thanks',
                'party_deny_reply': 'Sorry solo mode.',
                'enable_trade_block': True,
                'enable_duel_block': True,
                'trade_duel_reply': 'Sorry am busy'
            },

            # Auto-login settings
            'auto_login': {
                'username': '',
                'password': '',
                'server': 'Diyana',
                'character': 'Character1',
                'enable_auto_login': False,
                'auto_login_delay': 0,
                'delays': {
                    'after_enter': 500,
                    'after_update': 3000,
                    'after_launcher': 5000,
                    'after_login': 3000,
                    'after_server': 3000,
                    'after_character': 3000
                }
            },

            # Portal hack settings
            'portal_hack': {
                'target_address': 0,
                'search_start_address': 4194304,
                'bytes_to_read': 1000000,
                'search_pattern': '00000000',
                'enable_portal_hack': False
            },

            # Game interface settings
            'game_interface': {
                'window_title': 'Tantra Launcher',
                'key_delay': 50,
                'mouse_delay': 50
            },

            # Bot control settings
            'bot_control': {
                'enable_main_bot': False,
                'enable_trade_spam': False,
                'enable_click_spam': False,
                'main_loop_delay': 100,
                'trade_spam_delay': 50,
                'click_spam_delay': 50
            }
        }

        # Current configuration
        self.config = self.default_config.copy()

    def load_config(self) -> bool:
        """Load configuration from file."""
        try:
            if not os.path.exists(self.config_file):
                self.logger.info(f"Config file {self.config_file} not found, using defaults")
                return self.save_config()

            with open(self.config_file, 'r', encoding='utf-8') as f:
                loaded_config = json.load(f)

            # Merge with defaults to ensure all keys exist
            self.config = self._merge_configs(self.default_config, loaded_config)

            self.logger.info(f"Configuration loaded from {self.config_file}")
            return True

        except Exception as e:
            self.logger.error(f"Error loading configuration: {e}")
            self.config = self.default_config.copy()
            return False

    def save_config(self) -> bool:
        """Save current configuration to file."""
        try:
            # Update timestamp
            self.config['last_updated'] = datetime.now().isoformat()

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)

            self.logger.info(f"Configuration saved to {self.config_file}")
            return True

        except Exception as e:
            self.logger.error(f"Error saving configuration: {e}")
            return False

    def _merge_configs(self, default: Dict[str, Any], loaded: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively merge loaded config with defaults."""
        result = default.copy()

        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value

        return result

    def get_config(self, section: str = None) -> Dict[str, Any]:
        """Get configuration section or entire config."""
        if section is None:
            return self.config.copy()

        return self.config.get(section, {}).copy()

    def update_config(self, section: str, updates: Dict[str, Any]) -> bool:
        """Update a configuration section."""
        try:
            if section not in self.config:
                self.config[section] = {}

            self.config[section].update(updates)
            self.logger.info(f"Updated configuration section: {section}")
            return True

        except Exception as e:
            self.logger.error(f"Error updating config section {section}: {e}")
            return False

    def set_config_value(self, section: str, key: str, value: Any) -> bool:
        """Set a specific configuration value."""
        try:
            if section not in self.config:
                self.config[section] = {}

            self.config[section][key] = value
            self.logger.debug(f"Set {section}.{key} = {value}")
            return True

        except Exception as e:
            self.logger.error(f"Error setting config value {section}.{key}: {e}")
            return False

    def get_config_value(self, section: str, key: str, default: Any = None) -> Any:
        """Get a specific configuration value."""
        try:
            return self.config.get(section, {}).get(key, default)
        except Exception as e:
            self.logger.error(f"Error getting config value {section}.{key}: {e}")
            return default

    def reset_to_defaults(self, section: str = None) -> bool:
        """Reset configuration to defaults."""
        try:
            if section is None:
                self.config = self.default_config.copy()
                self.logger.info("Reset entire configuration to defaults")
            else:
                if section in self.default_config:
                    self.config[section] = self.default_config[section].copy()
                    self.logger.info(f"Reset section {section} to defaults")
                else:
                    self.logger.error(f"Unknown section: {section}")
                    return False

            return True

        except Exception as e:
            self.logger.error(f"Error resetting configuration: {e}")
            return False

    def export_config(self, filename: str) -> bool:
        """Export configuration to a different file."""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)

            self.logger.info(f"Configuration exported to {filename}")
            return True

        except Exception as e:
            self.logger.error(f"Error exporting configuration: {e}")
            return False

    def import_config(self, filename: str) -> bool:
        """Import configuration from a file."""
        try:
            if not os.path.exists(filename):
                self.logger.error(f"Import file {filename} not found")
                return False

            with open(filename, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)

            # Merge with defaults
            self.config = self._merge_configs(self.default_config, imported_config)

            self.logger.info(f"Configuration imported from {filename}")
            return True

        except Exception as e:
            self.logger.error(f"Error importing configuration: {e}")
            return False

    def validate_config(self) -> Dict[str, list]:
        """Validate current configuration and return any issues."""
        issues = {
            'errors': [],
            'warnings': []
        }

        try:
            # Validate character settings
            char_config = self.config.get('character', {})
            if char_config.get('heal_threshold', 0) < 1 or char_config.get('heal_threshold', 0) > 100:
                issues['warnings'].append("Heal threshold should be between 1-100")

            if char_config.get('tp_threshold', 0) < 1 or char_config.get('tp_threshold', 0) > 100:
                issues['warnings'].append("TP threshold should be between 1-100")

            # Validate auto-login settings
            login_config = self.config.get('auto_login', {})
            if login_config.get('enable_auto_login', False):
                if not login_config.get('username'):
                    issues['errors'].append("Username required for auto-login")
                if not login_config.get('password'):
                    issues['errors'].append("Password required for auto-login")

            # Validate combat settings
            combat_config = self.config.get('combat', {})
            if not combat_config.get('target_monsters'):
                issues['warnings'].append("No target monsters configured")

        except Exception as e:
            issues['errors'].append(f"Configuration validation error: {e}")

        return issues

    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of current configuration."""
        return {
            'version': self.config.get('version', 'Unknown'),
            'last_updated': self.config.get('last_updated', 'Never'),
            'sections': list(self.config.keys()),
            'auto_login_enabled': self.config.get('auto_login', {}).get('enable_auto_login', False),
            'main_bot_enabled': self.config.get('bot_control', {}).get('enable_main_bot', False),
            'portal_hack_enabled': self.config.get('portal_hack', {}).get('enable_portal_hack', False),
            'target_monsters_count': len(self.config.get('combat', {}).get('target_monsters', [])),
            'config_file': self.config_file
        }