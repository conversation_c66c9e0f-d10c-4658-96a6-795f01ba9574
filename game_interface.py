"""
Tantra Bot - Game Interface Module
Handles window control, keyboard/mouse input, and game window detection.
"""

import ctypes
import ctypes.wintypes
from ctypes import wintypes, windll
import time
import logging
from typing import Optional, Tuple, List
import win32gui
import win32con
import win32api
import win32process

class GameInterface:
    """Handles game window interaction and input simulation."""

    def __init__(self):
        self.window_handle = None
        self.window_title = "Tantra Launcher"
        self.process_id = None
        self.logger = logging.getLogger(__name__)

        # Windows API setup
        self.user32 = windll.user32
        self.kernel32 = windll.kernel32

        # Input simulation delays
        self.key_delay = 50
        self.mouse_delay = 50

    def find_game_window(self, window_title: str = None) -> bool:
        """
        Find and attach to the game window.

        Args:
            window_title: Title of the window to find

        Returns:
            bool: True if window found, False otherwise
        """
        if window_title:
            self.window_title = window_title

        try:
            # Find window by title
            self.window_handle = win32gui.FindWindow(None, self.window_title)

            if not self.window_handle:
                self.logger.error(f"Window '{self.window_title}' not found")
                return False

            # Get process ID
            _, self.process_id = win32process.GetWindowThreadProcessId(self.window_handle)

            self.logger.info(f"Found game window: {self.window_title} (Handle: {self.window_handle})")
            return True

        except Exception as e:
            self.logger.error(f"Error finding game window: {e}")
            return False

    def is_window_active(self) -> bool:
        """Check if the game window exists and is responsive."""
        if not self.window_handle:
            return False

        try:
            return win32gui.IsWindow(self.window_handle) and win32gui.IsWindowVisible(self.window_handle)
        except:
            return False

    def activate_window(self) -> bool:
        """Bring the game window to foreground."""
        if not self.window_handle:
            return False

        try:
            win32gui.SetForegroundWindow(self.window_handle)
            win32gui.ShowWindow(self.window_handle, win32con.SW_RESTORE)
            return True
        except Exception as e:
            self.logger.error(f"Error activating window: {e}")
            return False

    def get_window_rect(self) -> Optional[Tuple[int, int, int, int]]:
        """Get window rectangle (left, top, right, bottom)."""
        if not self.window_handle:
            return None

        try:
            return win32gui.GetWindowRect(self.window_handle)
        except:
            return None

    def send_key(self, key: str, hold_time: int = None) -> bool:
        """
        Send a key to the game window.

        Args:
            key: Key to send (e.g., 'a', 'F1', 'ENTER')
            hold_time: Time to hold key in milliseconds

        Returns:
            bool: True if successful
        """
        if not self.window_handle:
            return False

        try:
            # Convert key string to virtual key code
            vk_code = self._get_virtual_key_code(key)
            if vk_code is None:
                self.logger.error(f"Unknown key: {key}")
                return False

            # Send key down
            win32api.PostMessage(self.window_handle, win32con.WM_KEYDOWN, vk_code, 0)

            # Hold time
            if hold_time:
                time.sleep(hold_time / 1000.0)
            else:
                time.sleep(self.key_delay / 1000.0)

            # Send key up
            win32api.PostMessage(self.window_handle, win32con.WM_KEYUP, vk_code, 0)

            return True

        except Exception as e:
            self.logger.error(f"Error sending key {key}: {e}")
            return False

    def send_text(self, text: str) -> bool:
        """Send text to the game window."""
        if not self.window_handle:
            return False

        try:
            for char in text:
                win32api.PostMessage(self.window_handle, win32con.WM_CHAR, ord(char), 0)
                time.sleep(10 / 1000.0)  # Small delay between characters
            return True
        except Exception as e:
            self.logger.error(f"Error sending text: {e}")
            return False

    def click_at_position(self, x: int, y: int, button: str = "left") -> bool:
        """
        Click at specific coordinates in the game window.

        Args:
            x: X coordinate relative to window
            y: Y coordinate relative to window
            button: Mouse button ('left', 'right', 'middle')

        Returns:
            bool: True if successful
        """
        if not self.window_handle:
            return False

        try:
            # Convert coordinates to screen coordinates
            rect = self.get_window_rect()
            if not rect:
                return False

            screen_x = rect[0] + x
            screen_y = rect[1] + y

            # Get button constants
            if button == "left":
                down_msg = win32con.WM_LBUTTONDOWN
                up_msg = win32con.WM_LBUTTONUP
            elif button == "right":
                down_msg = win32con.WM_RBUTTONDOWN
                up_msg = win32con.WM_RBUTTONUP
            elif button == "middle":
                down_msg = win32con.WM_MBUTTONDOWN
                up_msg = win32con.WM_MBUTTONUP
            else:
                return False

            # Create lParam for coordinates
            lparam = win32api.MAKELONG(x, y)

            # Send mouse down
            win32api.PostMessage(self.window_handle, down_msg, 0, lparam)
            time.sleep(self.mouse_delay / 1000.0)

            # Send mouse up
            win32api.PostMessage(self.window_handle, up_msg, 0, lparam)

            return True

        except Exception as e:
            self.logger.error(f"Error clicking at ({x}, {y}): {e}")
            return False

    def _get_virtual_key_code(self, key: str) -> Optional[int]:
        """Convert key string to virtual key code."""
        key_map = {
            # Letters
            'a': 0x41, 'b': 0x42, 'c': 0x43, 'd': 0x44, 'e': 0x45, 'f': 0x46,
            'g': 0x47, 'h': 0x48, 'i': 0x49, 'j': 0x4A, 'k': 0x4B, 'l': 0x4C,
            'm': 0x4D, 'n': 0x4E, 'o': 0x4F, 'p': 0x50, 'q': 0x51, 'r': 0x52,
            's': 0x53, 't': 0x54, 'u': 0x55, 'v': 0x56, 'w': 0x57, 'x': 0x58,
            'y': 0x59, 'z': 0x5A,

            # Numbers
            '0': 0x30, '1': 0x31, '2': 0x32, '3': 0x33, '4': 0x34,
            '5': 0x35, '6': 0x36, '7': 0x37, '8': 0x38, '9': 0x39,

            # Function keys
            'F1': 0x70, 'F2': 0x71, 'F3': 0x72, 'F4': 0x73, 'F5': 0x74,
            'F6': 0x75, 'F7': 0x76, 'F8': 0x77, 'F9': 0x78, 'F10': 0x79,
            'F11': 0x7A, 'F12': 0x7B,

            # Special keys
            'ENTER': 0x0D, 'SPACE': 0x20, 'TAB': 0x09, 'ESCAPE': 0x1B,
            'BACKSPACE': 0x08, 'DELETE': 0x2E, 'INSERT': 0x2D,
            'HOME': 0x24, 'END': 0x23, 'PAGEUP': 0x21, 'PAGEDOWN': 0x22,

            # Arrow keys
            'UP': 0x26, 'DOWN': 0x28, 'LEFT': 0x25, 'RIGHT': 0x27,

            # Modifier keys
            'SHIFT': 0x10, 'CTRL': 0x11, 'ALT': 0x12,
        }

        return key_map.get(key.upper())

    def send_key_combination(self, keys: List[str], hold_time: int = None) -> bool:
        """
        Send a combination of keys (e.g., Ctrl+C).

        Args:
            keys: List of keys to press simultaneously
            hold_time: Time to hold keys in milliseconds

        Returns:
            bool: True if successful
        """
        if not self.window_handle:
            return False

        try:
            # Get virtual key codes
            vk_codes = []
            for key in keys:
                vk_code = self._get_virtual_key_code(key)
                if vk_code is None:
                    self.logger.error(f"Unknown key in combination: {key}")
                    return False
                vk_codes.append(vk_code)

            # Send all keys down
            for vk_code in vk_codes:
                win32api.PostMessage(self.window_handle, win32con.WM_KEYDOWN, vk_code, 0)

            # Hold time
            if hold_time:
                time.sleep(hold_time / 1000.0)
            else:
                time.sleep(self.key_delay / 1000.0)

            # Send all keys up (in reverse order)
            for vk_code in reversed(vk_codes):
                win32api.PostMessage(self.window_handle, win32con.WM_KEYUP, vk_code, 0)

            return True

        except Exception as e:
            self.logger.error(f"Error sending key combination {keys}: {e}")
            return False

    def unfocus_chat(self) -> bool:
        """Click on game area to unfocus chat input."""
        return self.click_at_position(520, 430)

    def set_delays(self, key_delay: int = None, mouse_delay: int = None):
        """Set input delays."""
        if key_delay is not None:
            self.key_delay = key_delay
        if mouse_delay is not None:
            self.mouse_delay = mouse_delay