"""
Tantra Bot - Portal Hack Module
Implements Mandara teleport exploit functionality.
"""

import time
import logging
from typing import Optional, List, Dict, Any
from memory_manager import MemoryManager
from game_interface import GameInterface

class PortalHack:
    """Manages Mandara portal teleport exploit functionality."""

    def __init__(self, memory_manager: MemoryManager, game_interface: GameInterface):
        self.memory = memory_manager
        self.game = game_interface
        self.logger = logging.getLogger(__name__)

        # Configuration
        self.config = {
            'target_address': 0,
            'search_start_address': 4194304,  # 0x400000
            'bytes_to_read': 1000000,
            'search_pattern': b'\x00\x00\x00\x00',  # Pattern to search for
            'enable_portal_hack': False
        }

        # Portal coordinates for different destinations
        self.portal_destinations = {
            'mandara_1': {'x': 100, 'y': 100},
            'mandara_2': {'x': 200, 'y': 200},
            'mandara_3': {'x': 300, 'y': 300},
            'mandara_4': {'x': 400, 'y': 400}
        }

        # Search results
        self.found_addresses = []

    def search_target_address(self) -> bool:
        """Search for the target address in memory."""
        if not self.config['enable_portal_hack']:
            return False

        try:
            self.logger.info("Starting target address search...")
            self.found_addresses.clear()

            start_addr = self.config['search_start_address']
            bytes_to_read = self.config['bytes_to_read']

            # Read memory chunk
            memory_data = self.memory.read_memory(start_addr, bytes_to_read)
            if not memory_data:
                self.logger.error("Failed to read memory for address search")
                return False

            # Search for pattern
            pattern = self.config['search_pattern']
            offset = 0

            while True:
                pos = memory_data.find(pattern, offset)
                if pos == -1:
                    break

                found_address = start_addr + pos
                self.found_addresses.append(found_address)
                offset = pos + 1

            self.logger.info(f"Found {len(self.found_addresses)} potential addresses")

            # Use the first found address as target
            if self.found_addresses:
                self.config['target_address'] = self.found_addresses[0]
                self.logger.info(f"Target address set to: 0x{self.config['target_address']:X}")
                return True
            else:
                self.logger.warning("No target addresses found")
                return False

        except Exception as e:
            self.logger.error(f"Error during address search: {e}")
            return False

    def search_multi_byte_pattern(self, pattern: bytes, start_addr: int = None,
                                 search_length: int = None) -> List[int]:
        """Search for a multi-byte pattern in memory."""
        try:
            if start_addr is None:
                start_addr = self.config['search_start_address']
            if search_length is None:
                search_length = self.config['bytes_to_read']

            self.logger.info(f"Searching for pattern: {pattern.hex()}")

            found_addresses = []
            memory_data = self.memory.read_memory(start_addr, search_length)

            if not memory_data:
                return found_addresses

            offset = 0
            while True:
                pos = memory_data.find(pattern, offset)
                if pos == -1:
                    break

                found_address = start_addr + pos
                found_addresses.append(found_address)
                offset = pos + 1

            self.logger.info(f"Found {len(found_addresses)} matches for pattern")
            return found_addresses

        except Exception as e:
            self.logger.error(f"Error searching multi-byte pattern: {e}")
            return []

    def activate_portal_hack(self) -> bool:
        """Activate the portal hack by writing to target address."""
        if not self.config['enable_portal_hack']:
            return False

        if self.config['target_address'] == 0:
            self.logger.error("Target address not set. Run address search first.")
            return False

        try:
            self.logger.info("Activating portal hack...")

            # Write hack value to target address
            success = self.memory.write_byte(self.config['target_address'], 1)

            if success:
                self.logger.info("Portal hack activated successfully")
                return True
            else:
                self.logger.error("Failed to write to target address")
                return False

        except Exception as e:
            self.logger.error(f"Error activating portal hack: {e}")
            return False

    def deactivate_portal_hack(self) -> bool:
        """Deactivate the portal hack by restoring original value."""
        if self.config['target_address'] == 0:
            return False

        try:
            self.logger.info("Deactivating portal hack...")

            # Write original value back
            success = self.memory.write_byte(self.config['target_address'], 0)

            if success:
                self.logger.info("Portal hack deactivated")
                return True
            else:
                self.logger.error("Failed to restore original value")
                return False

        except Exception as e:
            self.logger.error(f"Error deactivating portal hack: {e}")
            return False

    def teleport_to_destination(self, destination: str) -> bool:
        """Teleport to a specific destination using portal hack."""
        if not self.config['enable_portal_hack']:
            return False

        if destination not in self.portal_destinations:
            self.logger.error(f"Unknown destination: {destination}")
            return False

        try:
            # Activate portal hack first
            if not self.activate_portal_hack():
                return False

            coords = self.portal_destinations[destination]
            self.logger.info(f"Teleporting to {destination} at ({coords['x']}, {coords['y']})")

            # Click on the portal
            self.game.click_at_position(coords['x'], coords['y'])

            # Small delay for teleport to process
            time.sleep(1000)

            # Deactivate hack
            self.deactivate_portal_hack()

            return True

        except Exception as e:
            self.logger.error(f"Error teleporting to {destination}: {e}")
            return False

    def scan_portal_addresses(self) -> List[int]:
        """Scan for portal-related addresses in memory."""
        try:
            self.logger.info("Scanning for portal addresses...")

            # Common portal-related byte patterns
            portal_patterns = [
                b'\x60\x00\x00\x00',  # Common portal signature
                b'\x96\x00\x00\x00',  # Another portal pattern
                b'\xFF\xFF\xFF\xFF',  # Max value pattern
                b'\x01\x00\x00\x00'   # Simple flag pattern
            ]

            all_addresses = []

            for pattern in portal_patterns:
                addresses = self.search_multi_byte_pattern(pattern)
                all_addresses.extend(addresses)

            # Remove duplicates and sort
            unique_addresses = sorted(list(set(all_addresses)))

            self.logger.info(f"Found {len(unique_addresses)} unique portal addresses")
            return unique_addresses

        except Exception as e:
            self.logger.error(f"Error scanning portal addresses: {e}")
            return []

    def validate_target_address(self, address: int) -> bool:
        """Validate if an address is suitable for portal hack."""
        try:
            # Try to read from the address
            value = self.memory.read_memory(address, 4)
            if value is None:
                return False

            # Try to write and read back
            original = self.memory.read_memory(address, 1)
            if original is None:
                return False

            # Write test value
            if not self.memory.write_byte(address, 99):
                return False

            # Read back test value
            test_value = self.memory.read_memory(address, 1)
            if test_value is None or test_value[0] != 99:
                return False

            # Restore original value
            self.memory.write_memory(address, original)

            self.logger.info(f"Address 0x{address:X} validated successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error validating address 0x{address:X}: {e}")
            return False

    def auto_find_portal_address(self) -> bool:
        """Automatically find and validate a suitable portal address."""
        try:
            self.logger.info("Auto-finding portal address...")

            # Scan for potential addresses
            addresses = self.scan_portal_addresses()

            if not addresses:
                self.logger.error("No potential portal addresses found")
                return False

            # Test each address
            for address in addresses[:10]:  # Test first 10 addresses
                if self.validate_target_address(address):
                    self.config['target_address'] = address
                    self.logger.info(f"Auto-found portal address: 0x{address:X}")
                    return True

            self.logger.error("No valid portal addresses found")
            return False

        except Exception as e:
            self.logger.error(f"Error auto-finding portal address: {e}")
            return False

    def update_config(self, new_config: Dict[str, Any]):
        """Update portal hack configuration."""
        self.config.update(new_config)
        self.logger.info("Portal hack configuration updated")

    def get_portal_status(self) -> Dict[str, Any]:
        """Get current portal hack status."""
        return {
            'config': self.config.copy(),
            'found_addresses': self.found_addresses.copy(),
            'destinations': list(self.portal_destinations.keys()),
            'hack_active': self.config['target_address'] != 0
        }