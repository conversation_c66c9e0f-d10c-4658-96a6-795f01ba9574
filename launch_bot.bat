@echo off
title Tantra Bot - Epic Edition Launcher
echo.
echo ========================================
echo    🔥 TANTRA BOT - EPIC EDITION 🔥
echo ========================================
echo.
echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python 3.8+ first.
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python found!
echo.
echo Installing/updating dependencies...
pip install -r requirements.txt

echo.
echo ========================================
echo    🚀 LAUNCHING TANTRA BOT 🚀
echo ========================================
echo.
echo ⚠️  IMPORTANT NOTES:
echo    - Run this as Administrator for memory access
echo    - Make sure Tantra Online is running
echo    - Configure settings in the GUI
echo    - Press F8 to start/stop the bot
echo.
echo Starting bot in 3 seconds...
timeout /t 3 /nobreak >nul

python tantra_bot.py

echo.
echo Bot has exited. Press any key to close...
pause >nul