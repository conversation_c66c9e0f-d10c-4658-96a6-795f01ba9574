"""
Tantra Bot - Character Management Module
Handles HP/TP monitoring, healing, buffing, and character status tracking.
"""

import time
import logging
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from memory_manager import MemoryManager
from game_interface import GameInterface

class CharacterManager:
    """Manages character status, healing, and buffing."""

    def __init__(self, memory_manager: MemoryManager, game_interface: GameInterface):
        self.memory = memory_manager
        self.game = game_interface
        self.logger = logging.getLogger(__name__)

        # Memory addresses (from original AutoIt script)
        self.base_address_character = 0x109BE1D0
        self.max_hp_offset = 268  # 0x10C
        self.cur_hp_offset = 272  # 0x110
        self.tp_max_offset = 804  # 0x324
        self.tp_cur_offset = 808  # 0x328
        self.x_coord_offset = 9178  # 0x23DA
        self.y_coord_offset = 9182  # 0x23DE
        self.damage_offset = 264  # max_hp_offset - 4

        # Character status
        self.current_hp = 0
        self.max_hp = 0
        self.current_tp = 0
        self.max_tp = 0
        self.current_damage = 0
        self.x_position = 0
        self.y_position = 0
        self.is_dead = False

        # Buff tracking
        self.buff_timers = {
            'buff1': datetime.now(),
            'buff2': datetime.now(),
            'buff3': datetime.now(),
            'buff4': datetime.now(),
            'repair': datetime.now()
        }

        # Configuration
        self.config = {
            'heal_threshold': 70,  # Heal when HP below this percentage
            'tp_threshold': 70,    # Use TP pots when below this percentage
            'heal_skill_key': '7',
            'hp_pot_key': '8',
            'tp_pot_key': '9',
            'heal_delay': 2000,
            'hp_pot_delay': 2000,
            'tp_pot_delay': 2000,
            'buff_durations': {
                'buff1': 480000,  # 8 minutes
                'buff2': 600000,  # 10 minutes
                'buff3': 300000,  # 5 minutes
                'buff4': 300000,  # 5 minutes
                'repair': 300000  # 5 minutes
            },
            'auto_silf_coords': (150, 500),
            'enable_auto_heal': True,
            'enable_hp_pots': False,
            'enable_tp_pots': False,
            'enable_auto_silf': False,
            'enable_buffs': True,
            'enable_auto_repair': False
        }

    def update_character_status(self) -> bool:
        """Update character status from memory."""
        try:
            # Read HP values
            self.current_hp = self.memory.read_with_offset(
                self.base_address_character, self.cur_hp_offset
            ) or 0

            self.max_hp = self.memory.read_with_offset(
                self.base_address_character, self.max_hp_offset
            ) or 0

            # Read TP values
            self.current_tp = self.memory.read_with_offset(
                self.base_address_character, self.tp_cur_offset
            ) or 0

            self.max_tp = self.memory.read_with_offset(
                self.base_address_character, self.tp_max_offset
            ) or 0

            # Read damage value
            self.current_damage = self.memory.read_with_offset(
                self.base_address_character, self.damage_offset
            ) or 0

            # Read position
            self.x_position = self.memory.read_with_offset(
                self.base_address_character, self.x_coord_offset
            ) or 0

            self.y_position = self.memory.read_with_offset(
                self.base_address_character, self.y_coord_offset
            ) or 0

            # Check if dead
            self.is_dead = (self.current_hp == 0)

            return True

        except Exception as e:
            self.logger.error(f"Error updating character status: {e}")
            return False

    def get_hp_percentage(self) -> float:
        """Get current HP as percentage."""
        if self.max_hp == 0:
            return 0.0
        return (self.current_hp / self.max_hp) * 100

    def get_tp_percentage(self) -> float:
        """Get current TP as percentage."""
        if self.max_tp == 0:
            return 0.0
        return (self.current_tp / self.max_tp) * 100

    def needs_healing(self) -> bool:
        """Check if character needs healing."""
        if not self.config['enable_auto_heal']:
            return False
        return self.get_hp_percentage() <= self.config['heal_threshold'] and not self.is_dead

    def needs_hp_potion(self) -> bool:
        """Check if character needs HP potion."""
        if not self.config['enable_hp_pots']:
            return False
        return self.get_hp_percentage() <= self.config['heal_threshold'] and not self.is_dead

    def needs_tp_potion(self) -> bool:
        """Check if character needs TP potion."""
        if not self.config['enable_tp_pots']:
            return False
        return self.get_tp_percentage() <= self.config['tp_threshold'] and not self.is_dead

    def perform_healing(self) -> bool:
        """Perform healing action."""
        if not self.needs_healing():
            return False

        try:
            self.logger.info(f"Healing - HP: {self.current_hp}/{self.max_hp} ({self.get_hp_percentage():.1f}%)")

            # Use healing skill
            self.game.send_key(self.config['heal_skill_key'])
            time.sleep(self.config['heal_delay'] / 1000.0)

            return True

        except Exception as e:
            self.logger.error(f"Error performing healing: {e}")
            return False

    def use_hp_potion(self) -> bool:
        """Use HP potion."""
        if not self.needs_hp_potion():
            return False

        try:
            self.logger.info(f"Using HP potion - HP: {self.current_hp}/{self.max_hp} ({self.get_hp_percentage():.1f}%)")

            # Switch to F1 skill set and use HP potion
            self.game.send_key('F1')
            time.sleep(10)
            self.game.send_key(self.config['hp_pot_key'])
            time.sleep(self.config['hp_pot_delay'] / 1000.0)

            return True

        except Exception as e:
            self.logger.error(f"Error using HP potion: {e}")
            return False

    def use_tp_potion(self) -> bool:
        """Use TP potion."""
        if not self.needs_tp_potion():
            return False

        try:
            self.logger.info(f"Using TP potion - TP: {self.current_tp}/{self.max_tp} ({self.get_tp_percentage():.1f}%)")

            # Switch to F1 skill set and use TP potion
            self.game.send_key('F1')
            time.sleep(10)
            self.game.send_key(self.config['tp_pot_key'])
            time.sleep(self.config['tp_pot_delay'] / 1000.0)

            return True

        except Exception as e:
            self.logger.error(f"Error using TP potion: {e}")
            return False

    def auto_resurrect(self) -> bool:
        """Automatically resurrect using Silfrijan."""
        if not self.is_dead or not self.config['enable_auto_silf']:
            return False

        try:
            self.logger.info("Character is dead, attempting auto-resurrection")

            # Click on Silfrijan coordinates
            x, y = self.config['auto_silf_coords']
            self.game.click_at_position(x, y)
            time.sleep(500)

            # Click OK button
            self.game.click_at_position(515, 370)
            time.sleep(500)

            # Perform healing after resurrection
            self.perform_healing()
            self.use_hp_potion()
            self.use_tp_potion()

            return True

        except Exception as e:
            self.logger.error(f"Error during auto-resurrection: {e}")
            return False

    def needs_buff(self, buff_name: str) -> bool:
        """Check if a buff needs to be refreshed."""
        if not self.config['enable_buffs']:
            return False

        if buff_name not in self.buff_timers:
            return True

        duration = self.config['buff_durations'].get(buff_name, 300000)
        elapsed = (datetime.now() - self.buff_timers[buff_name]).total_seconds() * 1000

        return elapsed >= duration

    def cast_buff(self, buff_number: int) -> bool:
        """Cast a buff spell."""
        if not self.config['enable_buffs']:
            return False

        buff_name = f'buff{buff_number}'
        if not self.needs_buff(buff_name):
            return False

        try:
            self.logger.info(f"Casting {buff_name}")

            # Switch to F2 skill set
            self.game.send_key('F2')
            time.sleep(100)

            # Cast the buff
            self.game.send_key(str(buff_number))
            time.sleep(100)

            # Update buff timer
            self.buff_timers[buff_name] = datetime.now()

            # Wait for buff to complete
            time.sleep(2000)

            # Switch back to F1 skill set
            self.game.send_key('F1')

            return True

        except Exception as e:
            self.logger.error(f"Error casting {buff_name}: {e}")
            return False

    def check_repair_needed(self) -> bool:
        """Check if equipment needs repair."""
        if not self.config['enable_auto_repair']:
            return False

        # Read minimum damage rate from memory
        minimum_damage_rate_addr = 0x10A03278
        min_damage_rate = self.memory.read_int32(minimum_damage_rate_addr) or 100

        # Check if damage rate is below threshold (default 2000 from original script)
        return min_damage_rate < 2000

    def perform_repair(self) -> bool:
        """Perform equipment repair."""
        if not self.check_repair_needed():
            return False

        try:
            self.logger.info("Equipment needs repair")

            # Switch to F2 skill set
            self.game.send_key('F2')
            time.sleep(2000)

            # Use repair skill (key 9)
            self.game.send_key('9')

            # Update repair timer
            self.buff_timers['repair'] = datetime.now()

            time.sleep(25)

            # Switch back to F1 skill set
            self.game.send_key('F1')

            return True

        except Exception as e:
            self.logger.error(f"Error performing repair: {e}")
            return False

    def get_character_status(self) -> Dict[str, Any]:
        """Get complete character status."""
        return {
            'hp': self.current_hp,
            'max_hp': self.max_hp,
            'hp_percentage': self.get_hp_percentage(),
            'tp': self.current_tp,
            'max_tp': self.max_tp,
            'tp_percentage': self.get_tp_percentage(),
            'damage': self.current_damage,
            'x_position': self.x_position,
            'y_position': self.y_position,
            'is_dead': self.is_dead,
            'needs_healing': self.needs_healing(),
            'needs_hp_potion': self.needs_hp_potion(),
            'needs_tp_potion': self.needs_tp_potion(),
            'needs_repair': self.check_repair_needed()
        }

    def update_config(self, new_config: Dict[str, Any]):
        """Update configuration settings."""
        self.config.update(new_config)
        self.logger.info("Character manager configuration updated")

    def perform_maintenance(self) -> bool:
        """Perform all maintenance tasks (healing, potions, buffs, repair)."""
        try:
            # Update character status first
            self.update_character_status()

            # Handle death
            if self.is_dead:
                return self.auto_resurrect()

            # Perform healing
            if self.needs_healing():
                self.perform_healing()

            # Use potions
            if self.needs_hp_potion():
                self.use_hp_potion()

            if self.needs_tp_potion():
                self.use_tp_potion()

            # Cast buffs
            for i in range(1, 5):
                if self.needs_buff(f'buff{i}'):
                    self.cast_buff(i)
                    break  # Only cast one buff per cycle

            # Perform repair
            if self.check_repair_needed():
                self.perform_repair()

            return True

        except Exception as e:
            self.logger.error(f"Error during maintenance: {e}")
            return False