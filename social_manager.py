"""
<PERSON><PERSON>t - Social Manager <PERSON><PERSON><PERSON>
Handles PM replies, party management, trade/duel blocking, and chat monitoring.
"""

import time
import logging
from typing import Optional, Dict, Any
from memory_manager import MemoryManager
from game_interface import GameInterface

class SocialManager:
    """Manages social interactions including PM, party, trade, and duel handling."""

    def __init__(self, memory_manager: MemoryManager, game_interface: GameInterface):
        self.memory = memory_manager
        self.game = game_interface
        self.logger = logging.getLogger(__name__)

        # Memory addresses
        self.chat_msg_base = 0x109BE200
        self.chat_msg_offset = 2848  # 0xB20

        self.base_address_dialog = 0x109BE23C
        self.party_offset = 667  # 0x29B

        self.base_address_dialog_trade = 0x109BE248
        self.trade_offset = 270  # 0x10E

        self.base_address_dialog_duel = 0x10A03DF0

        # Dialog state tracking
        self.last_party_dialog = None
        self.last_trade_dialog = None
        self.last_duel_dialog = None

        # Configuration
        self.config = {
            'enable_pm_reply': False,
            'pm_reply_text': 'Pil0t p0h',
            'enable_party_accept': True,
            'party_accept_reply': 'Thanks',
            'party_deny_reply': 'Sorry solo mode.',
            'enable_trade_block': True,
            'enable_duel_block': True,
            'trade_duel_reply': 'Sorry am busy'
        }

        # Initialize dialog states
        self._initialize_dialog_states()

    def _initialize_dialog_states(self):
        """Initialize dialog state tracking."""
        try:
            # Set initial dialog values to 49 (closed state)
            party_addr = self._get_address_with_offset(self.base_address_dialog, self.party_offset)
            if party_addr:
                self.memory.write_byte(party_addr, 49)
                self.last_party_dialog = self._get_dialog_data(party_addr)

            trade_addr = self._get_address_with_offset(self.base_address_dialog_trade, self.trade_offset)
            if trade_addr:
                self.memory.write_byte(trade_addr, 49)
                self.last_trade_dialog = self._get_dialog_data(trade_addr)

            self.memory.write_byte(self.base_address_dialog_duel, 49)
            self.last_duel_dialog = self._get_dialog_data(self.base_address_dialog_duel)

        except Exception as e:
            self.logger.error(f"Error initializing dialog states: {e}")

    def _get_address_with_offset(self, base_address: int, offset: int) -> Optional[int]:
        """Get address by reading pointer and adding offset."""
        try:
            pointer_value = self.memory.get_pointer_value(base_address)
            if pointer_value:
                return pointer_value + offset
            return None
        except:
            return None

    def _get_dialog_data(self, address: int) -> Optional[str]:
        """Read dialog data from memory address."""
        try:
            return self.memory.read_string(address, 64)
        except:
            return None

    def _get_chat_message(self) -> Optional[str]:
        """Get the latest chat message."""
        try:
            chat_addr = self._get_address_with_offset(self.chat_msg_base, self.chat_msg_offset)
            if chat_addr:
                return self.memory.read_string(chat_addr, 256)
            return None
        except Exception as e:
            self.logger.error(f"Error reading chat message: {e}")
            return None

    def check_private_message(self) -> bool:
        """Check for incoming private messages and reply if enabled."""
        if not self.config['enable_pm_reply']:
            return False

        try:
            chat_message = self._get_chat_message()
            if not chat_message:
                return False

            # Check if message contains "From" (indicating a PM)
            if "From" in chat_message:
                self.logger.info(f"Private message detected: {chat_message}")

                # Click to unfocus chat
                self.game.unfocus_chat()

                # Send reply
                self.game.send_key('v')  # Open PM reply
                time.sleep(50)

                self.game.send_text(self.config['pm_reply_text'])
                time.sleep(500)

                self.game.send_key('ENTER')
                self.game.unfocus_chat()

                return True

        except Exception as e:
            self.logger.error(f"Error checking private message: {e}")

        return False

    def check_party_invitation(self) -> bool:
        """Check for party invitations and handle them."""
        try:
            party_addr = self._get_address_with_offset(self.base_address_dialog, self.party_offset)
            if not party_addr:
                return False

            current_dialog = self._get_dialog_data(party_addr)

            # Check if dialog state changed (new invitation)
            if current_dialog != self.last_party_dialog:
                self.logger.info("Party invitation detected")

                if self.config['enable_party_accept']:
                    # Accept party invitation
                    self.game.click_at_position(400, 400)  # Click dialog
                    self.game.send_key('ENTER')
                    time.sleep(500)
                    self.game.send_key('ENTER')
                    time.sleep(500)

                    # Send acceptance message
                    self.game.send_text(f"/g {self.config['party_accept_reply']}")
                    time.sleep(500)
                    self.game.send_key('ENTER')

                else:
                    # Decline party invitation
                    self.game.click_at_position(400, 400)  # Click dialog
                    self.game.click_at_position(600, 420)  # Click decline
                    time.sleep(500)
                    self.game.send_key('ENTER')
                    time.sleep(500)

                    # Send decline message
                    self.game.send_text(f"/s {self.config['party_deny_reply']}")
                    time.sleep(500)
                    self.game.send_key('ENTER')

                # Reset dialog state
                self.memory.write_byte(party_addr, 49)
                self.last_party_dialog = self._get_dialog_data(party_addr)

                return True

        except Exception as e:
            self.logger.error(f"Error checking party invitation: {e}")

        return False

    def check_trade_request(self) -> bool:
        """Check for trade requests and block them if enabled."""
        if not self.config['enable_trade_block']:
            return False

        try:
            trade_addr = self._get_address_with_offset(self.base_address_dialog_trade, self.trade_offset)
            if not trade_addr:
                return False

            current_dialog = self._get_dialog_data(trade_addr)

            # Check if dialog state changed (new trade request)
            if current_dialog != self.last_trade_dialog:
                self.logger.info("Trade request detected - blocking")

                # Click dialog and decline
                self.game.click_at_position(400, 400)  # Click dialog
                self.game.click_at_position(600, 420)  # Click decline
                time.sleep(500)
                self.game.send_key('ENTER')
                time.sleep(500)
                self.game.send_key('ENTER')
                time.sleep(500)

                # Send decline message
                self.game.send_text(f"/s {self.config['trade_duel_reply']}")
                time.sleep(500)
                self.game.send_key('ENTER')

                # Reset dialog state
                self.memory.write_byte(trade_addr, 49)
                self.last_trade_dialog = self._get_dialog_data(trade_addr)

                return True

        except Exception as e:
            self.logger.error(f"Error checking trade request: {e}")

        return False

    def check_duel_request(self) -> bool:
        """Check for duel requests and block them if enabled."""
        if not self.config['enable_duel_block']:
            return False

        try:
            current_dialog = self._get_dialog_data(self.base_address_dialog_duel)

            # Check if dialog state changed (new duel request)
            if current_dialog != self.last_duel_dialog:
                self.logger.info("Duel request detected - blocking")

                # Click dialog and decline multiple times (duel dialogs can be tricky)
                self.game.click_at_position(400, 400)  # Click dialog
                self.game.click_at_position(600, 420)  # Click decline
                time.sleep(500)
                self.game.click_at_position(400, 400)  # Click dialog again
                self.game.send_key('ENTER')
                time.sleep(500)
                self.game.send_key('ENTER')
                time.sleep(500)
                self.game.send_key('ENTER')
                time.sleep(500)

                # Send decline message
                self.game.send_text(f"/s {self.config['trade_duel_reply']}")
                time.sleep(500)
                self.game.send_key('ENTER')

                # Reset dialog state
                self.memory.write_byte(self.base_address_dialog_duel, 49)
                self.last_duel_dialog = self._get_dialog_data(self.base_address_dialog_duel)

                return True

        except Exception as e:
            self.logger.error(f"Error checking duel request: {e}")

        return False

    def handle_all_social_events(self) -> Dict[str, bool]:
        """Handle all social events and return status."""
        results = {
            'pm_handled': False,
            'party_handled': False,
            'trade_handled': False,
            'duel_handled': False
        }

        try:
            results['pm_handled'] = self.check_private_message()
            results['party_handled'] = self.check_party_invitation()
            results['trade_handled'] = self.check_trade_request()
            results['duel_handled'] = self.check_duel_request()

        except Exception as e:
            self.logger.error(f"Error handling social events: {e}")

        return results

    def update_config(self, new_config: Dict[str, Any]):
        """Update social manager configuration."""
        self.config.update(new_config)
        self.logger.info("Social manager configuration updated")

    def get_social_status(self) -> Dict[str, Any]:
        """Get current social manager status."""
        return {
            'config': self.config.copy(),
            'last_party_dialog': self.last_party_dialog,
            'last_trade_dialog': self.last_trade_dialog,
            'last_duel_dialog': self.last_duel_dialog
        }