"""
Tantra Bot - Main Controller
Coordinates all modules and provides the main bot loop with hotkey system.
"""

import time
import threading
import logging
import keyboard
import sys
from typing import Dict, Any, Optional
from datetime import datetime

# Import all modules
from memory_manager import MemoryManager
from game_interface import GameInterface
from character_manager import <PERSON><PERSON>anager
from combat_system import CombatSystem
from social_manager import <PERSON>Manager
from auto_login import <PERSON>Login
from portal_hack import PortalHack
from config_manager import ConfigManager
from gui_interface import EpicGUI

class TantraBot:
    """Main Tantra Bot controller that coordinates all modules."""

    def __init__(self):
        self.logger = self._setup_logging()
        self.logger.info("🔥 Initializing Tantra Bot - Epic Edition 🔥")

        # Bot state
        self.is_running = False
        self.main_loop_thread = None
        self.trade_spam_thread = None
        self.click_spam_thread = None

        # Initialize configuration manager
        self.config_manager = ConfigManager()
        self.config_manager.load_config()

        # Initialize core modules
        self.memory = MemoryManager()
        self.game = GameInterface()
        self.character = CharacterManager(self.memory, self.game)
        self.combat = CombatSystem(self.memory, self.game, self.character)
        self.social = SocialManager(self.memory, self.game)
        self.auto_login = AutoLogin(self.game)
        self.portal_hack = PortalHack(self.memory, self.game)

        # Initialize GUI
        self.gui = EpicGUI(self)

        # Load configuration into modules
        self._load_module_configs()

        # Setup hotkeys
        self._setup_hotkeys()

        self.logger.info("✅ Tantra Bot initialized successfully")

    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('tantra_bot.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        return logging.getLogger(__name__)

    def _load_module_configs(self):
        """Load configuration into all modules."""
        try:
            # Update character manager
            char_config = self.config_manager.get_config('character')
            self.character.update_config(char_config)

            # Update combat system
            combat_config = self.config_manager.get_config('combat')
            self.combat.update_config(combat_config)

            # Update social manager
            social_config = self.config_manager.get_config('social')
            self.social.update_config(social_config)

            # Update auto-login
            login_config = self.config_manager.get_config('auto_login')
            self.auto_login.update_config(login_config)

            # Update portal hack
            portal_config = self.config_manager.get_config('portal_hack')
            self.portal_hack.update_config(portal_config)

            # Update game interface
            game_config = self.config_manager.get_config('game_interface')
            if 'window_title' in game_config:
                self.game.window_title = game_config['window_title']
            if 'key_delay' in game_config and 'mouse_delay' in game_config:
                self.game.set_delays(game_config['key_delay'], game_config['mouse_delay'])

            self.logger.info("Module configurations loaded")

        except Exception as e:
            self.logger.error(f"Error loading module configs: {e}")

    def _setup_hotkeys(self):
        """Setup global hotkeys for bot control."""
        try:
            # F8 - Start/Stop main bot
            keyboard.add_hotkey('f8', self._hotkey_toggle_main_bot)

            # F9 - Trade spam mode
            keyboard.add_hotkey('f9', self._hotkey_toggle_trade_spam)

            # F10 - Emergency stop
            keyboard.add_hotkey('f10', self._hotkey_emergency_stop)

            # F11 - Click spam mode
            keyboard.add_hotkey('f11', self._hotkey_toggle_click_spam)

            # Shift+Alt+M - Show main menu
            keyboard.add_hotkey('shift+alt+m', self._hotkey_show_gui)

            # Shift+Alt+A - Auto-login
            keyboard.add_hotkey('shift+alt+a', self._hotkey_auto_login)

            # Shift+Alt+T - Portal hack menu
            keyboard.add_hotkey('shift+alt+t', self._hotkey_portal_hack)

            # Alt+1-4 - Position capture
            keyboard.add_hotkey('alt+1', lambda: self._hotkey_capture_position(1))
            keyboard.add_hotkey('alt+2', lambda: self._hotkey_capture_position(2))
            keyboard.add_hotkey('alt+3', lambda: self._hotkey_capture_position(3))
            keyboard.add_hotkey('alt+4', lambda: self._hotkey_capture_position(4))

            self.logger.info("Hotkeys registered successfully")

        except Exception as e:
            self.logger.error(f"Error setting up hotkeys: {e}")

    def initialize_game_connection(self) -> bool:
        """Initialize connection to the game process."""
        try:
            self.logger.info("Connecting to game process...")

            # Attach to game process
            if not self.memory.attach_to_process("HTLauncher.exe"):
                self.logger.error("Failed to attach to game process")
                return False

            # Find game window
            if not self.game.find_game_window():
                self.logger.error("Failed to find game window")
                return False

            self.logger.info("✅ Game connection established")
            return True

        except Exception as e:
            self.logger.error(f"Error initializing game connection: {e}")
            return False

    def start_main_bot(self) -> bool:
        """Start the main bot loop."""
        if self.is_running:
            self.logger.warning("Bot is already running")
            return False

        try:
            # Initialize game connection
            if not self.initialize_game_connection():
                return False

            self.is_running = True
            self.main_loop_thread = threading.Thread(target=self._main_bot_loop, daemon=True)
            self.main_loop_thread.start()

            self.logger.info("🚀 Main bot started")
            return True

        except Exception as e:
            self.logger.error(f"Error starting main bot: {e}")
            return False

    def stop_main_bot(self):
        """Stop the main bot loop."""
        self.is_running = False
        if self.main_loop_thread and self.main_loop_thread.is_alive():
            self.main_loop_thread.join(timeout=5)
        self.logger.info("🛑 Main bot stopped")

    def _main_bot_loop(self):
        """Main bot execution loop."""
        self.logger.info("Main bot loop started")

        while self.is_running:
            try:
                # Check for disconnection and auto-login
                if self.auto_login.config['enable_auto_login']:
                    self.auto_login.check_disconnection()

                # Handle social events
                self.social.handle_all_social_events()

                # Perform character maintenance
                self.character.perform_maintenance()

                # Execute combat cycle
                self.combat.combat_cycle()

                # Small delay to prevent excessive CPU usage
                loop_delay = self.config_manager.get_config_value('bot_control', 'main_loop_delay', 100)
                time.sleep(loop_delay / 1000.0)

            except Exception as e:
                self.logger.error(f"Error in main bot loop: {e}")
                time.sleep(1)  # Prevent rapid error loops

        self.logger.info("Main bot loop ended")

    def start_trade_spam(self):
        """Start trade spam mode."""
        if self.trade_spam_thread and self.trade_spam_thread.is_alive():
            return

        self.trade_spam_thread = threading.Thread(target=self._trade_spam_loop, daemon=True)
        self.trade_spam_thread.start()
        self.logger.info("Trade spam started")

    def _trade_spam_loop(self):
        """Trade spam execution loop."""
        while True:
            try:
                self.game.send_text("/s WTS ITEMS CHEAP!")
                self.game.send_key('ENTER')

                delay = self.config_manager.get_config_value('bot_control', 'trade_spam_delay', 50)
                time.sleep(delay / 1000.0)

            except Exception as e:
                self.logger.error(f"Error in trade spam loop: {e}")
                break

    def start_click_spam(self):
        """Start click spam mode."""
        if self.click_spam_thread and self.click_spam_thread.is_alive():
            return

        self.click_spam_thread = threading.Thread(target=self._click_spam_loop, daemon=True)
        self.click_spam_thread.start()
        self.logger.info("Click spam started")

    def _click_spam_loop(self):
        """Click spam execution loop."""
        while True:
            try:
                self.game.click_at_position(500, 400)

                delay = self.config_manager.get_config_value('bot_control', 'click_spam_delay', 50)
                time.sleep(delay / 1000.0)

            except Exception as e:
                self.logger.error(f"Error in click spam loop: {e}")
                break

    # Hotkey handlers
    def _hotkey_toggle_main_bot(self):
        """Hotkey handler for F8 - Toggle main bot."""
        if self.is_running:
            self.stop_main_bot()
        else:
            self.start_main_bot()

    def _hotkey_toggle_trade_spam(self):
        """Hotkey handler for F9 - Toggle trade spam."""
        self.start_trade_spam()

    def _hotkey_emergency_stop(self):
        """Hotkey handler for F10 - Emergency stop."""
        self.stop_main_bot()
        self.logger.info("🚨 EMERGENCY STOP ACTIVATED")

    def _hotkey_toggle_click_spam(self):
        """Hotkey handler for F11 - Toggle click spam."""
        self.start_click_spam()

    def _hotkey_show_gui(self):
        """Hotkey handler for Shift+Alt+M - Show GUI."""
        if not self.gui.root:
            self.gui.create_gui()
        self.gui.root.deiconify()
        self.gui.root.lift()

    def _hotkey_auto_login(self):
        """Hotkey handler for Shift+Alt+A - Auto-login."""
        self.auto_login.perform_login_sequence()

    def _hotkey_portal_hack(self):
        """Hotkey handler for Shift+Alt+T - Portal hack."""
        self.portal_hack.auto_find_portal_address()

    def _hotkey_capture_position(self, position_num: int):
        """Hotkey handler for Alt+1-4 - Capture position."""
        # Get current mouse position (would need additional implementation)
        self.logger.info(f"Position {position_num} capture requested")

    def get_bot_status(self) -> Dict[str, Any]:
        """Get comprehensive bot status."""
        return {
            'is_running': self.is_running,
            'game_connected': self.game.is_window_active(),
            'character_status': self.character.get_character_status(),
            'combat_status': self.combat.get_combat_status(),
            'social_status': self.social.get_social_status(),
            'login_status': self.auto_login.get_login_status(),
            'portal_status': self.portal_hack.get_portal_status(),
            'config_summary': self.config_manager.get_config_summary()
        }

    def save_configuration(self) -> bool:
        """Save current configuration."""
        return self.config_manager.save_config()

    def load_configuration(self) -> bool:
        """Load configuration and update modules."""
        if self.config_manager.load_config():
            self._load_module_configs()
            return True
        return False

    def run_gui(self):
        """Run the GUI interface."""
        try:
            self.gui.create_gui()
            self.gui.root.mainloop()
        except Exception as e:
            self.logger.error(f"Error running GUI: {e}")

    def shutdown(self):
        """Shutdown the bot and cleanup resources."""
        self.logger.info("Shutting down Tantra Bot...")

        # Stop all running threads
        self.stop_main_bot()

        # Save configuration
        self.save_configuration()

        # Close memory handle
        self.memory.close()

        self.logger.info("✅ Tantra Bot shutdown complete")


def main():
    """Main entry point."""
    try:
        # Create bot instance
        bot = TantraBot()

        # Run GUI
        bot.run_gui()

    except KeyboardInterrupt:
        print("\n🛑 Bot interrupted by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
    finally:
        try:
            bot.shutdown()
        except:
            pass


if __name__ == "__main__":
    main()