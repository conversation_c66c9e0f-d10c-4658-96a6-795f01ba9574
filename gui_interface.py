"""
Tantra Bot - GUI Interface Module
Creates modern GUI using tkinter with epic design and all configuration options.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import logging
from typing import Dict, Any, Callable, Optional
from datetime import datetime

class EpicGUI:
    """Epic GUI interface for Tantra Bot with dramatic visual effects."""

    def __init__(self, bot_controller):
        self.bot = bot_controller
        self.logger = logging.getLogger(__name__)

        # GUI state
        self.root = None
        self.is_running = False
        self.status_update_thread = None

        # Epic color scheme
        self.colors = {
            'bg_primary': '#0a0a0a',      # Deep black
            'bg_secondary': '#1a1a1a',    # Dark gray
            'bg_accent': '#2a2a2a',       # Medium gray
            'accent_primary': '#ff6600',   # Epic orange
            'accent_secondary': '#ff9933', # Light orange
            'text_primary': '#ffffff',     # White
            'text_secondary': '#cccccc',   # Light gray
            'text_accent': '#ff6600',      # Orange text
            'success': '#00ff00',          # Green
            'warning': '#ffff00',          # Yellow
            'error': '#ff0000',            # Red
            'button_hover': '#ff8833',     # Orange hover
            'border': '#444444'            # Border gray
        }

        # Fonts
        self.fonts = {
            'title': ('Arial Black', 16, 'bold'),
            'header': ('Arial', 12, 'bold'),
            'normal': ('Arial', 10),
            'small': ('Arial', 8),
            'mono': ('Courier New', 9)
        }

        # Status tracking
        self.last_status = {}

    def create_gui(self):
        """Create the main GUI window with epic design."""
        self.root = tk.Tk()
        self.root.title("🔥 TANTRA BOT - EPIC EDITION 🔥")
        self.root.geometry("1200x800")
        self.root.configure(bg=self.colors['bg_primary'])
        self.root.resizable(True, True)

        # Configure styles
        self._configure_styles()

        # Create main layout
        self._create_main_layout()

        # Start status updates
        self._start_status_updates()

        self.logger.info("Epic GUI created successfully")

    def _configure_styles(self):
        """Configure ttk styles for epic appearance."""
        style = ttk.Style()

        # Configure notebook (tabs)
        style.theme_use('clam')

        style.configure('Epic.TNotebook',
                       background=self.colors['bg_primary'],
                       borderwidth=2,
                       relief='solid')

        style.configure('Epic.TNotebook.Tab',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'],
                       padding=[20, 10],
                       font=self.fonts['header'])

        style.map('Epic.TNotebook.Tab',
                 background=[('selected', self.colors['accent_primary']),
                           ('active', self.colors['button_hover'])])

        # Configure frames
        style.configure('Epic.TFrame',
                       background=self.colors['bg_secondary'],
                       relief='solid',
                       borderwidth=1)

        # Configure labels
        style.configure('Epic.TLabel',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'],
                       font=self.fonts['normal'])

        style.configure('Title.TLabel',
                       background=self.colors['bg_primary'],
                       foreground=self.colors['accent_primary'],
                       font=self.fonts['title'])

        style.configure('Header.TLabel',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['accent_secondary'],
                       font=self.fonts['header'])

        # Configure buttons
        style.configure('Epic.TButton',
                       background=self.colors['accent_primary'],
                       foreground=self.colors['text_primary'],
                       font=self.fonts['header'],
                       padding=[10, 5])

        style.map('Epic.TButton',
                 background=[('active', self.colors['button_hover']),
                           ('pressed', self.colors['accent_secondary'])])

        # Configure checkboxes
        style.configure('Epic.TCheckbutton',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'],
                       font=self.fonts['normal'])

        # Configure entries
        style.configure('Epic.TEntry',
                       fieldbackground=self.colors['bg_accent'],
                       foreground=self.colors['text_primary'],
                       bordercolor=self.colors['border'],
                       font=self.fonts['normal'])

    def _create_main_layout(self):
        """Create the main layout with tabs and controls."""
        # Title frame
        title_frame = tk.Frame(self.root, bg=self.colors['bg_primary'], height=60)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)

        # Epic title with effects
        title_label = tk.Label(title_frame,
                              text="🔥 TANTRA BOT - ULTIMATE EDITION 🔥",
                              bg=self.colors['bg_primary'],
                              fg=self.colors['accent_primary'],
                              font=self.fonts['title'])
        title_label.pack(expand=True)

        # Status bar
        self.status_frame = tk.Frame(self.root, bg=self.colors['bg_secondary'], height=30)
        self.status_frame.pack(fill='x', padx=10, pady=2)
        self.status_frame.pack_propagate(False)

        self.status_label = tk.Label(self.status_frame,
                                   text="🚀 Bot Status: Ready",
                                   bg=self.colors['bg_secondary'],
                                   fg=self.colors['success'],
                                   font=self.fonts['normal'])
        self.status_label.pack(side='left', padx=10, pady=5)

        # Main control buttons
        control_frame = tk.Frame(self.root, bg=self.colors['bg_primary'], height=50)
        control_frame.pack(fill='x', padx=10, pady=5)
        control_frame.pack_propagate(False)

        self._create_control_buttons(control_frame)

        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root, style='Epic.TNotebook')
        self.notebook.pack(fill='both', expand=True, padx=10, pady=5)

        # Create tabs
        self._create_main_tab()
        self._create_character_tab()
        self._create_combat_tab()
        self._create_social_tab()
        self._create_login_tab()
        self._create_portal_tab()
        self._create_logs_tab()

    def _create_control_buttons(self, parent):
        """Create main control buttons."""
        # Start/Stop button
        self.start_button = tk.Button(parent,
                                    text="🚀 START BOT",
                                    bg=self.colors['success'],
                                    fg=self.colors['text_primary'],
                                    font=self.fonts['header'],
                                    command=self._toggle_bot,
                                    width=15,
                                    height=2)
        self.start_button.pack(side='left', padx=5, pady=5)

        # Emergency stop
        emergency_button = tk.Button(parent,
                                   text="🛑 EMERGENCY STOP",
                                   bg=self.colors['error'],
                                   fg=self.colors['text_primary'],
                                   font=self.fonts['header'],
                                   command=self._emergency_stop,
                                   width=15,
                                   height=2)
        emergency_button.pack(side='left', padx=5, pady=5)

        # Save config
        save_button = tk.Button(parent,
                              text="💾 SAVE CONFIG",
                              bg=self.colors['accent_primary'],
                              fg=self.colors['text_primary'],
                              font=self.fonts['header'],
                              command=self._save_config,
                              width=15,
                              height=2)
        save_button.pack(side='left', padx=5, pady=5)

        # Load config
        load_button = tk.Button(parent,
                              text="📁 LOAD CONFIG",
                              bg=self.colors['accent_primary'],
                              fg=self.colors['text_primary'],
                              font=self.fonts['header'],
                              command=self._load_config,
                              width=15,
                              height=2)
        load_button.pack(side='left', padx=5, pady=5)

    def _create_main_tab(self):
        """Create main status and overview tab."""
        main_frame = ttk.Frame(self.notebook, style='Epic.TFrame')
        self.notebook.add(main_frame, text="🏠 MAIN")

        # Status display
        status_frame = tk.LabelFrame(main_frame,
                                   text="🔥 EPIC STATUS DISPLAY",
                                   bg=self.colors['bg_secondary'],
                                   fg=self.colors['accent_primary'],
                                   font=self.fonts['header'])
        status_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Character status
        char_frame = tk.Frame(status_frame, bg=self.colors['bg_secondary'])
        char_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(char_frame, text="⚔️ CHARACTER STATUS:",
                bg=self.colors['bg_secondary'],
                fg=self.colors['accent_secondary'],
                font=self.fonts['header']).pack(anchor='w')

        self.char_status_text = tk.Text(char_frame,
                                      height=6,
                                      bg=self.colors['bg_accent'],
                                      fg=self.colors['text_primary'],
                                      font=self.fonts['mono'],
                                      state='disabled')
        self.char_status_text.pack(fill='x', pady=5)

        # Combat status
        combat_frame = tk.Frame(status_frame, bg=self.colors['bg_secondary'])
        combat_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(combat_frame, text="⚔️ COMBAT STATUS:",
                bg=self.colors['bg_secondary'],
                fg=self.colors['accent_secondary'],
                font=self.fonts['header']).pack(anchor='w')

        self.combat_status_text = tk.Text(combat_frame,
                                        height=4,
                                        bg=self.colors['bg_accent'],
                                        fg=self.colors['text_primary'],
                                        font=self.fonts['mono'],
                                        state='disabled')
        self.combat_status_text.pack(fill='x', pady=5)

        # Quick actions
        actions_frame = tk.LabelFrame(main_frame,
                                    text="⚡ QUICK ACTIONS",
                                    bg=self.colors['bg_secondary'],
                                    fg=self.colors['accent_primary'],
                                    font=self.fonts['header'])
        actions_frame.pack(fill='x', padx=10, pady=10)

        # Action buttons
        btn_frame = tk.Frame(actions_frame, bg=self.colors['bg_secondary'])
        btn_frame.pack(fill='x', padx=10, pady=10)

        tk.Button(btn_frame, text="🩺 HEAL NOW",
                 bg=self.colors['success'], fg=self.colors['text_primary'],
                 command=lambda: self.bot.character.perform_healing()).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🔮 CAST BUFFS",
                 bg=self.colors['accent_primary'], fg=self.colors['text_primary'],
                 command=self._cast_all_buffs).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🎯 FIND TARGET",
                 bg=self.colors['warning'], fg=self.colors['bg_primary'],
                 command=lambda: self.bot.combat.find_target()).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🔧 REPAIR",
                 bg=self.colors['accent_secondary'], fg=self.colors['text_primary'],
                 command=lambda: self.bot.character.perform_repair()).pack(side='left', padx=5)

    def _create_character_tab(self):
        """Create character management tab."""
        char_frame = ttk.Frame(self.notebook, style='Epic.TFrame')
        self.notebook.add(char_frame, text="⚔️ CHARACTER")

        # Healing settings
        heal_frame = tk.LabelFrame(char_frame,
                                 text="🩺 HEALING SETTINGS",
                                 bg=self.colors['bg_secondary'],
                                 fg=self.colors['accent_primary'],
                                 font=self.fonts['header'])
        heal_frame.pack(fill='x', padx=10, pady=5)

        # Heal threshold
        tk.Label(heal_frame, text="Heal Threshold (%):",
                bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).grid(row=0, column=0, sticky='w', padx=5, pady=2)

        self.heal_threshold_var = tk.StringVar(value="70")
        tk.Entry(heal_frame, textvariable=self.heal_threshold_var,
                bg=self.colors['bg_accent'], fg=self.colors['text_primary'], width=10).grid(row=0, column=1, padx=5, pady=2)

        # Enable auto heal
        self.auto_heal_var = tk.BooleanVar(value=True)
        tk.Checkbutton(heal_frame, text="Enable Auto Heal",
                      variable=self.auto_heal_var,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).grid(row=0, column=2, padx=5, pady=2)

        # Buff settings
        buff_frame = tk.LabelFrame(char_frame,
                                 text="🔮 BUFF SETTINGS",
                                 bg=self.colors['bg_secondary'],
                                 fg=self.colors['accent_primary'],
                                 font=self.fonts['header'])
        buff_frame.pack(fill='x', padx=10, pady=5)

        # Buff checkboxes
        self.buff_vars = {}
        for i in range(1, 5):
            self.buff_vars[f'buff{i}'] = tk.BooleanVar(value=True)
            tk.Checkbutton(buff_frame, text=f"Buff {i}",
                          variable=self.buff_vars[f'buff{i}'],
                          bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).grid(row=0, column=i-1, padx=5, pady=2)

    def _create_combat_tab(self):
        """Create combat settings tab."""
        combat_frame = ttk.Frame(self.notebook, style='Epic.TFrame')
        self.notebook.add(combat_frame, text="⚔️ COMBAT")

        # Target monsters
        target_frame = tk.LabelFrame(combat_frame,
                                   text="🎯 TARGET MONSTERS",
                                   bg=self.colors['bg_secondary'],
                                   fg=self.colors['accent_primary'],
                                   font=self.fonts['header'])
        target_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Monster list
        self.monster_listbox = tk.Listbox(target_frame,
                                        bg=self.colors['bg_accent'],
                                        fg=self.colors['text_primary'],
                                        font=self.fonts['normal'],
                                        height=8)
        self.monster_listbox.pack(fill='both', expand=True, padx=5, pady=5)

        # Monster controls
        monster_controls = tk.Frame(target_frame, bg=self.colors['bg_secondary'])
        monster_controls.pack(fill='x', padx=5, pady=5)

        self.monster_entry = tk.Entry(monster_controls,
                                    bg=self.colors['bg_accent'],
                                    fg=self.colors['text_primary'],
                                    font=self.fonts['normal'])
        self.monster_entry.pack(side='left', fill='x', expand=True, padx=2)

        tk.Button(monster_controls, text="ADD",
                 bg=self.colors['success'], fg=self.colors['text_primary'],
                 command=self._add_monster).pack(side='left', padx=2)

        tk.Button(monster_controls, text="REMOVE",
                 bg=self.colors['error'], fg=self.colors['text_primary'],
                 command=self._remove_monster).pack(side='left', padx=2)

        # Combat options
        options_frame = tk.LabelFrame(combat_frame,
                                    text="⚡ COMBAT OPTIONS",
                                    bg=self.colors['bg_secondary'],
                                    fg=self.colors['accent_primary'],
                                    font=self.fonts['header'])
        options_frame.pack(fill='x', padx=10, pady=5)

        self.combat_vars = {
            'enable_skills': tk.BooleanVar(value=True),
            'enable_r_key': tk.BooleanVar(value=True),
            'enable_looting': tk.BooleanVar(value=True),
            'enable_monster_select': tk.BooleanVar(value=True)
        }

        row = 0
        for key, var in self.combat_vars.items():
            text = key.replace('_', ' ').title()
            tk.Checkbutton(options_frame, text=text,
                          variable=var,
                          bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).grid(row=row//2, column=row%2, sticky='w', padx=5, pady=2)
            row += 1

    def _create_social_tab(self):
        """Create social features tab."""
        social_frame = ttk.Frame(self.notebook, style='Epic.TFrame')
        self.notebook.add(social_frame, text="💬 SOCIAL")

        # PM settings
        pm_frame = tk.LabelFrame(social_frame,
                               text="📧 PRIVATE MESSAGE SETTINGS",
                               bg=self.colors['bg_secondary'],
                               fg=self.colors['accent_primary'],
                               font=self.fonts['header'])
        pm_frame.pack(fill='x', padx=10, pady=5)

        self.pm_reply_var = tk.BooleanVar(value=False)
        tk.Checkbutton(pm_frame, text="Enable PM Auto-Reply",
                      variable=self.pm_reply_var,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).pack(anchor='w', padx=5, pady=2)

        tk.Label(pm_frame, text="Reply Text:",
                bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).pack(anchor='w', padx=5, pady=2)

        self.pm_text_var = tk.StringVar(value="Pil0t p0h")
        tk.Entry(pm_frame, textvariable=self.pm_text_var,
                bg=self.colors['bg_accent'], fg=self.colors['text_primary'],
                width=50).pack(fill='x', padx=5, pady=2)

        # Party settings
        party_frame = tk.LabelFrame(social_frame,
                                  text="👥 PARTY SETTINGS",
                                  bg=self.colors['bg_secondary'],
                                  fg=self.colors['accent_primary'],
                                  font=self.fonts['header'])
        party_frame.pack(fill='x', padx=10, pady=5)

        self.party_accept_var = tk.BooleanVar(value=True)
        tk.Checkbutton(party_frame, text="Auto-Accept Party Invitations",
                      variable=self.party_accept_var,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).pack(anchor='w', padx=5, pady=2)

        # Trade/Duel blocking
        block_frame = tk.LabelFrame(social_frame,
                                  text="🚫 BLOCKING SETTINGS",
                                  bg=self.colors['bg_secondary'],
                                  fg=self.colors['accent_primary'],
                                  font=self.fonts['header'])
        block_frame.pack(fill='x', padx=10, pady=5)

        self.trade_block_var = tk.BooleanVar(value=True)
        tk.Checkbutton(block_frame, text="Block Trade Requests",
                      variable=self.trade_block_var,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).pack(anchor='w', padx=5, pady=2)

        self.duel_block_var = tk.BooleanVar(value=True)
        tk.Checkbutton(block_frame, text="Block Duel Requests",
                      variable=self.duel_block_var,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).pack(anchor='w', padx=5, pady=2)

    def _create_login_tab(self):
        """Create auto-login tab."""
        login_frame = ttk.Frame(self.notebook, style='Epic.TFrame')
        self.notebook.add(login_frame, text="🔐 LOGIN")

        # Login settings
        settings_frame = tk.LabelFrame(login_frame,
                                     text="🔐 AUTO-LOGIN SETTINGS",
                                     bg=self.colors['bg_secondary'],
                                     fg=self.colors['accent_primary'],
                                     font=self.fonts['header'])
        settings_frame.pack(fill='x', padx=10, pady=5)

        # Enable auto-login
        self.auto_login_var = tk.BooleanVar(value=False)
        tk.Checkbutton(settings_frame, text="Enable Auto-Login",
                      variable=self.auto_login_var,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).grid(row=0, column=0, columnspan=2, sticky='w', padx=5, pady=2)

        # Username
        tk.Label(settings_frame, text="Username:",
                bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).grid(row=1, column=0, sticky='w', padx=5, pady=2)

        self.username_var = tk.StringVar()
        tk.Entry(settings_frame, textvariable=self.username_var,
                bg=self.colors['bg_accent'], fg=self.colors['text_primary'], width=20).grid(row=1, column=1, padx=5, pady=2)

        # Password
        tk.Label(settings_frame, text="Password:",
                bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).grid(row=2, column=0, sticky='w', padx=5, pady=2)

        self.password_var = tk.StringVar()
        tk.Entry(settings_frame, textvariable=self.password_var, show="*",
                bg=self.colors['bg_accent'], fg=self.colors['text_primary'], width=20).grid(row=2, column=1, padx=5, pady=2)

        # Server selection
        tk.Label(settings_frame, text="Server:",
                bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).grid(row=3, column=0, sticky='w', padx=5, pady=2)

        self.server_var = tk.StringVar(value="Diyana")
        server_combo = ttk.Combobox(settings_frame, textvariable=self.server_var,
                                  values=["Manas", "Diyana", "Kriya", "Samadi", "Warzone"],
                                  state="readonly", width=18)
        server_combo.grid(row=3, column=1, padx=5, pady=2)

        # Character selection
        tk.Label(settings_frame, text="Character:",
                bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).grid(row=4, column=0, sticky='w', padx=5, pady=2)

        self.character_var = tk.StringVar(value="Character1")
        char_combo = ttk.Combobox(settings_frame, textvariable=self.character_var,
                                values=["Character1", "Character2", "Character3"],
                                state="readonly", width=18)
        char_combo.grid(row=4, column=1, padx=5, pady=2)

        # Login button
        tk.Button(settings_frame, text="🚀 LOGIN NOW",
                 bg=self.colors['success'], fg=self.colors['text_primary'],
                 font=self.fonts['header'],
                 command=self._perform_login).grid(row=5, column=0, columnspan=2, pady=10)

    def _create_portal_tab(self):
        """Create portal hack tab."""
        portal_frame = ttk.Frame(self.notebook, style='Epic.TFrame')
        self.notebook.add(portal_frame, text="🌀 PORTAL")

        # Portal hack settings
        hack_frame = tk.LabelFrame(portal_frame,
                                 text="🌀 PORTAL HACK SETTINGS",
                                 bg=self.colors['bg_secondary'],
                                 fg=self.colors['accent_primary'],
                                 font=self.fonts['header'])
        hack_frame.pack(fill='x', padx=10, pady=5)

        # Enable portal hack
        self.portal_hack_var = tk.BooleanVar(value=False)
        tk.Checkbutton(hack_frame, text="Enable Portal Hack",
                      variable=self.portal_hack_var,
                      bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).pack(anchor='w', padx=5, pady=2)

        # Target address
        tk.Label(hack_frame, text="Target Address (hex):",
                bg=self.colors['bg_secondary'], fg=self.colors['text_primary']).pack(anchor='w', padx=5, pady=2)

        self.portal_address_var = tk.StringVar(value="0x0")
        tk.Entry(hack_frame, textvariable=self.portal_address_var,
                bg=self.colors['bg_accent'], fg=self.colors['text_primary'], width=20).pack(anchor='w', padx=5, pady=2)

        # Portal controls
        controls_frame = tk.Frame(hack_frame, bg=self.colors['bg_secondary'])
        controls_frame.pack(fill='x', padx=5, pady=5)

        tk.Button(controls_frame, text="🔍 SEARCH ADDRESS",
                 bg=self.colors['accent_primary'], fg=self.colors['text_primary'],
                 command=self._search_portal_address).pack(side='left', padx=2)

        tk.Button(controls_frame, text="✅ ACTIVATE",
                 bg=self.colors['success'], fg=self.colors['text_primary'],
                 command=self._activate_portal).pack(side='left', padx=2)

        tk.Button(controls_frame, text="❌ DEACTIVATE",
                 bg=self.colors['error'], fg=self.colors['text_primary'],
                 command=self._deactivate_portal).pack(side='left', padx=2)

    def _create_logs_tab(self):
        """Create logs and status tab."""
        logs_frame = ttk.Frame(self.notebook, style='Epic.TFrame')
        self.notebook.add(logs_frame, text="📋 LOGS")

        # Log display
        log_frame = tk.LabelFrame(logs_frame,
                                text="📋 BOT LOGS",
                                bg=self.colors['bg_secondary'],
                                fg=self.colors['accent_primary'],
                                font=self.fonts['header'])
        log_frame.pack(fill='both', expand=True, padx=10, pady=5)

        self.log_text = scrolledtext.ScrolledText(log_frame,
                                                bg=self.colors['bg_accent'],
                                                fg=self.colors['text_primary'],
                                                font=self.fonts['mono'],
                                                height=20,
                                                state='disabled')
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)

        # Log controls
        log_controls = tk.Frame(log_frame, bg=self.colors['bg_secondary'])
        log_controls.pack(fill='x', padx=5, pady=5)

        tk.Button(log_controls, text="🗑️ CLEAR LOGS",
                 bg=self.colors['error'], fg=self.colors['text_primary'],
                 command=self._clear_logs).pack(side='left', padx=2)

        tk.Button(log_controls, text="💾 SAVE LOGS",
                 bg=self.colors['accent_primary'], fg=self.colors['text_primary'],
                 command=self._save_logs).pack(side='left', padx=2)

    # Event handlers
    def _toggle_bot(self):
        """Toggle bot on/off."""
        if self.bot.is_running:
            self.bot.stop_main_bot()
            self.start_button.config(text="🚀 START BOT", bg=self.colors['success'])
        else:
            if self.bot.start_main_bot():
                self.start_button.config(text="🛑 STOP BOT", bg=self.colors['error'])

    def _emergency_stop(self):
        """Emergency stop all bot functions."""
        self.bot.stop_main_bot()
        self.start_button.config(text="🚀 START BOT", bg=self.colors['success'])
        self._log_message("🚨 EMERGENCY STOP ACTIVATED", "error")

    def _save_config(self):
        """Save current configuration."""
        if self.bot.save_configuration():
            self._log_message("💾 Configuration saved successfully", "success")
        else:
            self._log_message("❌ Failed to save configuration", "error")

    def _load_config(self):
        """Load configuration."""
        if self.bot.load_configuration():
            self._log_message("📁 Configuration loaded successfully", "success")
            self._update_gui_from_config()
        else:
            self._log_message("❌ Failed to load configuration", "error")

    def _cast_all_buffs(self):
        """Cast all buffs."""
        for i in range(1, 5):
            self.bot.character.cast_buff(i)
        self._log_message("🔮 Casting all buffs", "info")

    def _add_monster(self):
        """Add monster to target list."""
        monster_name = self.monster_entry.get().strip()
        if monster_name:
            self.bot.combat.add_target_monster(monster_name)
            self.monster_listbox.insert(tk.END, monster_name)
            self.monster_entry.delete(0, tk.END)
            self._log_message(f"🎯 Added target monster: {monster_name}", "info")

    def _remove_monster(self):
        """Remove selected monster from target list."""
        selection = self.monster_listbox.curselection()
        if selection:
            monster_name = self.monster_listbox.get(selection[0])
            self.bot.combat.remove_target_monster(monster_name)
            self.monster_listbox.delete(selection[0])
            self._log_message(f"🗑️ Removed target monster: {monster_name}", "info")

    def _perform_login(self):
        """Perform auto-login."""
        # Update login config
        login_config = {
            'enable_auto_login': self.auto_login_var.get(),
            'username': self.username_var.get(),
            'password': self.password_var.get(),
            'server': self.server_var.get(),
            'character': self.character_var.get()
        }
        self.bot.auto_login.update_config(login_config)

        # Perform login
        if self.bot.auto_login.perform_login_sequence():
            self._log_message("🔐 Auto-login successful", "success")
        else:
            self._log_message("❌ Auto-login failed", "error")

    def _search_portal_address(self):
        """Search for portal address."""
        if self.bot.portal_hack.auto_find_portal_address():
            address = self.bot.portal_hack.config['target_address']
            self.portal_address_var.set(f"0x{address:X}")
            self._log_message(f"🔍 Portal address found: 0x{address:X}", "success")
        else:
            self._log_message("❌ Portal address search failed", "error")

    def _activate_portal(self):
        """Activate portal hack."""
        if self.bot.portal_hack.activate_portal_hack():
            self._log_message("🌀 Portal hack activated", "success")
        else:
            self._log_message("❌ Portal hack activation failed", "error")

    def _deactivate_portal(self):
        """Deactivate portal hack."""
        if self.bot.portal_hack.deactivate_portal_hack():
            self._log_message("🌀 Portal hack deactivated", "info")
        else:
            self._log_message("❌ Portal hack deactivation failed", "error")

    def _clear_logs(self):
        """Clear log display."""
        self.log_text.config(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state='disabled')

    def _save_logs(self):
        """Save logs to file."""
        try:
            with open(f"tantra_bot_gui_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt", 'w') as f:
                f.write(self.log_text.get(1.0, tk.END))
            self._log_message("💾 Logs saved to file", "success")
        except Exception as e:
            self._log_message(f"❌ Failed to save logs: {e}", "error")

    def _log_message(self, message: str, level: str = "info"):
        """Add message to log display."""
        timestamp = datetime.now().strftime("%H:%M:%S")

        # Color coding
        colors = {
            "info": self.colors['text_primary'],
            "success": self.colors['success'],
            "warning": self.colors['warning'],
            "error": self.colors['error']
        }

        color = colors.get(level, self.colors['text_primary'])

        self.log_text.config(state='normal')
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state='disabled')

    def _update_gui_from_config(self):
        """Update GUI elements from loaded configuration."""
        try:
            # Character settings
            char_config = self.bot.config_manager.get_config('character')
            self.heal_threshold_var.set(str(char_config.get('heal_threshold', 70)))
            self.auto_heal_var.set(char_config.get('enable_auto_heal', True))

            # Combat settings
            combat_config = self.bot.config_manager.get_config('combat')
            for key, var in self.combat_vars.items():
                var.set(combat_config.get(key, True))

            # Update monster list
            self.monster_listbox.delete(0, tk.END)
            for monster in combat_config.get('target_monsters', []):
                self.monster_listbox.insert(tk.END, monster)

            # Social settings
            social_config = self.bot.config_manager.get_config('social')
            self.pm_reply_var.set(social_config.get('enable_pm_reply', False))
            self.pm_text_var.set(social_config.get('pm_reply_text', 'Pil0t p0h'))
            self.party_accept_var.set(social_config.get('enable_party_accept', True))
            self.trade_block_var.set(social_config.get('enable_trade_block', True))
            self.duel_block_var.set(social_config.get('enable_duel_block', True))

            # Login settings
            login_config = self.bot.config_manager.get_config('auto_login')
            self.auto_login_var.set(login_config.get('enable_auto_login', False))
            self.username_var.set(login_config.get('username', ''))
            self.password_var.set(login_config.get('password', ''))
            self.server_var.set(login_config.get('server', 'Diyana'))
            self.character_var.set(login_config.get('character', 'Character1'))

            # Portal settings
            portal_config = self.bot.config_manager.get_config('portal_hack')
            self.portal_hack_var.set(portal_config.get('enable_portal_hack', False))
            address = portal_config.get('target_address', 0)
            self.portal_address_var.set(f"0x{address:X}" if address else "0x0")

        except Exception as e:
            self._log_message(f"❌ Error updating GUI from config: {e}", "error")

    def _start_status_updates(self):
        """Start the status update thread."""
        self.is_running = True
        self.status_update_thread = threading.Thread(target=self._status_update_loop, daemon=True)
        self.status_update_thread.start()

    def _status_update_loop(self):
        """Status update loop."""
        while self.is_running and self.root:
            try:
                # Update status displays
                self._update_status_displays()
                time.sleep(1)  # Update every second
            except Exception as e:
                self.logger.error(f"Error in status update loop: {e}")
                time.sleep(5)

    def _update_status_displays(self):
        """Update status text displays."""
        try:
            if not self.root:
                return

            # Get bot status
            status = self.bot.get_bot_status()

            # Update main status label
            if self.bot.is_running:
                self.status_label.config(text="🚀 Bot Status: RUNNING", fg=self.colors['success'])
            else:
                self.status_label.config(text="🛑 Bot Status: STOPPED", fg=self.colors['error'])

            # Update character status
            char_status = status.get('character_status', {})
            char_text = f"""HP: {char_status.get('hp', 0)}/{char_status.get('max_hp', 0)} ({char_status.get('hp_percentage', 0):.1f}%)
TP: {char_status.get('tp', 0)}/{char_status.get('max_tp', 0)} ({char_status.get('tp_percentage', 0):.1f}%)
Position: ({char_status.get('x_position', 0)}, {char_status.get('y_position', 0)})
Status: {'DEAD' if char_status.get('is_dead', False) else 'ALIVE'}
Needs Healing: {'YES' if char_status.get('needs_healing', False) else 'NO'}
Needs Repair: {'YES' if char_status.get('needs_repair', False) else 'NO'}"""

            self.char_status_text.config(state='normal')
            self.char_status_text.delete(1.0, tk.END)
            self.char_status_text.insert(1.0, char_text)
            self.char_status_text.config(state='disabled')

            # Update combat status
            combat_status = status.get('combat_status', {})
            combat_text = f"""Current Target: {combat_status.get('current_target', 'None')}
Next Skill: {combat_status.get('next_skill', 'None')}
Skills Enabled: {'YES' if combat_status.get('enable_skills', False) else 'NO'}
Monster Select: {'YES' if combat_status.get('enable_monster_select', False) else 'NO'}"""

            self.combat_status_text.config(state='normal')
            self.combat_status_text.delete(1.0, tk.END)
            self.combat_status_text.insert(1.0, combat_text)
            self.combat_status_text.config(state='disabled')

        except Exception as e:
            self.logger.error(f"Error updating status displays: {e}")

    def run(self):
        """Run the GUI main loop."""
        if self.root:
            self.root.mainloop()

    def close(self):
        """Close the GUI."""
        self.is_running = False
        if self.root:
            self.root.quit()
            self.root.destroy()
            self.root = None