"""
Tantra Bot - GUI Interface Module
Creates modern GUI using tkinter with epic design and all configuration options.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import logging
from typing import Dict, Any, Callable, Optional
from datetime import datetime

class EpicGUI:
    """Epic GUI interface for Tantra Bot with dramatic visual effects."""

    def __init__(self, bot_controller):
        self.bot = bot_controller
        self.logger = logging.getLogger(__name__)

        # GUI state
        self.root = None
        self.is_running = False
        self.status_update_thread = None

        # Epic color scheme
        self.colors = {
            'bg_primary': '#0a0a0a',      # Deep black
            'bg_secondary': '#1a1a1a',    # Dark gray
            'bg_accent': '#2a2a2a',       # Medium gray
            'accent_primary': '#ff6600',   # Epic orange
            'accent_secondary': '#ff9933', # Light orange
            'text_primary': '#ffffff',     # White
            'text_secondary': '#cccccc',   # Light gray
            'text_accent': '#ff6600',      # Orange text
            'success': '#00ff00',          # Green
            'warning': '#ffff00',          # Yellow
            'error': '#ff0000',            # Red
            'button_hover': '#ff8833',     # Orange hover
            'border': '#444444'            # Border gray
        }

        # Fonts
        self.fonts = {
            'title': ('Arial Black', 16, 'bold'),
            'header': ('Arial', 12, 'bold'),
            'normal': ('Arial', 10),
            'small': ('Arial', 8),
            'mono': ('Courier New', 9)
        }

        # Status tracking
        self.last_status = {}

    def create_gui(self):
        """Create the main GUI window with epic design."""
        self.root = tk.Tk()
        self.root.title("🔥 TANTRA BOT - EPIC EDITION 🔥")
        self.root.geometry("1200x800")
        self.root.configure(bg=self.colors['bg_primary'])
        self.root.resizable(True, True)

        # Configure styles
        self._configure_styles()

        # Create main layout
        self._create_main_layout()

        # Start status updates
        self._start_status_updates()

        self.logger.info("Epic GUI created successfully")

    def _configure_styles(self):
        """Configure ttk styles for epic appearance."""
        style = ttk.Style()

        # Configure notebook (tabs)
        style.theme_use('clam')

        style.configure('Epic.TNotebook',
                       background=self.colors['bg_primary'],
                       borderwidth=2,
                       relief='solid')

        style.configure('Epic.TNotebook.Tab',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'],
                       padding=[20, 10],
                       font=self.fonts['header'])

        style.map('Epic.TNotebook.Tab',
                 background=[('selected', self.colors['accent_primary']),
                           ('active', self.colors['button_hover'])])

        # Configure frames
        style.configure('Epic.TFrame',
                       background=self.colors['bg_secondary'],
                       relief='solid',
                       borderwidth=1)

        # Configure labels
        style.configure('Epic.TLabel',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'],
                       font=self.fonts['normal'])

        style.configure('Title.TLabel',
                       background=self.colors['bg_primary'],
                       foreground=self.colors['accent_primary'],
                       font=self.fonts['title'])

        style.configure('Header.TLabel',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['accent_secondary'],
                       font=self.fonts['header'])

        # Configure buttons
        style.configure('Epic.TButton',
                       background=self.colors['accent_primary'],
                       foreground=self.colors['text_primary'],
                       font=self.fonts['header'],
                       padding=[10, 5])

        style.map('Epic.TButton',
                 background=[('active', self.colors['button_hover']),
                           ('pressed', self.colors['accent_secondary'])])

        # Configure checkboxes
        style.configure('Epic.TCheckbutton',
                       background=self.colors['bg_secondary'],
                       foreground=self.colors['text_primary'],
                       font=self.fonts['normal'])

        # Configure entries
        style.configure('Epic.TEntry',
                       fieldbackground=self.colors['bg_accent'],
                       foreground=self.colors['text_primary'],
                       bordercolor=self.colors['border'],
                       font=self.fonts['normal'])

    def _create_main_layout(self):
        """Create the main layout with tabs and controls."""
        # Title frame
        title_frame = tk.Frame(self.root, bg=self.colors['bg_primary'], height=60)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)

        # Epic title with effects
        title_label = tk.Label(title_frame,
                              text="🔥 TANTRA BOT - ULTIMATE EDITION 🔥",
                              bg=self.colors['bg_primary'],
                              fg=self.colors['accent_primary'],
                              font=self.fonts['title'])
        title_label.pack(expand=True)

        # Status bar
        self.status_frame = tk.Frame(self.root, bg=self.colors['bg_secondary'], height=30)
        self.status_frame.pack(fill='x', padx=10, pady=2)
        self.status_frame.pack_propagate(False)

        self.status_label = tk.Label(self.status_frame,
                                   text="🚀 Bot Status: Ready",
                                   bg=self.colors['bg_secondary'],
                                   fg=self.colors['success'],
                                   font=self.fonts['normal'])
        self.status_label.pack(side='left', padx=10, pady=5)

        # Main control buttons
        control_frame = tk.Frame(self.root, bg=self.colors['bg_primary'], height=50)
        control_frame.pack(fill='x', padx=10, pady=5)
        control_frame.pack_propagate(False)

        self._create_control_buttons(control_frame)

        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root, style='Epic.TNotebook')
        self.notebook.pack(fill='both', expand=True, padx=10, pady=5)

        # Create tabs
        self._create_main_tab()
        self._create_character_tab()
        self._create_combat_tab()
        self._create_social_tab()
        self._create_login_tab()
        self._create_portal_tab()
        self._create_logs_tab()

    def _create_control_buttons(self, parent):
        """Create main control buttons."""
        # Start/Stop button
        self.start_button = tk.Button(parent,
                                    text="🚀 START BOT",
                                    bg=self.colors['success'],
                                    fg=self.colors['text_primary'],
                                    font=self.fonts['header'],
                                    command=self._toggle_bot,
                                    width=15,
                                    height=2)
        self.start_button.pack(side='left', padx=5, pady=5)

        # Emergency stop
        emergency_button = tk.Button(parent,
                                   text="🛑 EMERGENCY STOP",
                                   bg=self.colors['error'],
                                   fg=self.colors['text_primary'],
                                   font=self.fonts['header'],
                                   command=self._emergency_stop,
                                   width=15,
                                   height=2)
        emergency_button.pack(side='left', padx=5, pady=5)

        # Save config
        save_button = tk.Button(parent,
                              text="💾 SAVE CONFIG",
                              bg=self.colors['accent_primary'],
                              fg=self.colors['text_primary'],
                              font=self.fonts['header'],
                              command=self._save_config,
                              width=15,
                              height=2)
        save_button.pack(side='left', padx=5, pady=5)

        # Load config
        load_button = tk.Button(parent,
                              text="📁 LOAD CONFIG",
                              bg=self.colors['accent_primary'],
                              fg=self.colors['text_primary'],
                              font=self.fonts['header'],
                              command=self._load_config,
                              width=15,
                              height=2)
        load_button.pack(side='left', padx=5, pady=5)

    def _create_main_tab(self):
        """Create main status and overview tab."""
        main_frame = ttk.Frame(self.notebook, style='Epic.TFrame')
        self.notebook.add(main_frame, text="🏠 MAIN")

        # Status display
        status_frame = tk.LabelFrame(main_frame,
                                   text="🔥 EPIC STATUS DISPLAY",
                                   bg=self.colors['bg_secondary'],
                                   fg=self.colors['accent_primary'],
                                   font=self.fonts['header'])
        status_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Character status
        char_frame = tk.Frame(status_frame, bg=self.colors['bg_secondary'])
        char_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(char_frame, text="⚔️ CHARACTER STATUS:",
                bg=self.colors['bg_secondary'],
                fg=self.colors['accent_secondary'],
                font=self.fonts['header']).pack(anchor='w')

        self.char_status_text = tk.Text(char_frame,
                                      height=6,
                                      bg=self.colors['bg_accent'],
                                      fg=self.colors['text_primary'],
                                      font=self.fonts['mono'],
                                      state='disabled')
        self.char_status_text.pack(fill='x', pady=5)

        # Combat status
        combat_frame = tk.Frame(status_frame, bg=self.colors['bg_secondary'])
        combat_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(combat_frame, text="⚔️ COMBAT STATUS:",
                bg=self.colors['bg_secondary'],
                fg=self.colors['accent_secondary'],
                font=self.fonts['header']).pack(anchor='w')

        self.combat_status_text = tk.Text(combat_frame,
                                        height=4,
                                        bg=self.colors['bg_accent'],
                                        fg=self.colors['text_primary'],
                                        font=self.fonts['mono'],
                                        state='disabled')
        self.combat_status_text.pack(fill='x', pady=5)

        # Quick actions
        actions_frame = tk.LabelFrame(main_frame,
                                    text="⚡ QUICK ACTIONS",
                                    bg=self.colors['bg_secondary'],
                                    fg=self.colors['accent_primary'],
                                    font=self.fonts['header'])
        actions_frame.pack(fill='x', padx=10, pady=10)

        # Action buttons
        btn_frame = tk.Frame(actions_frame, bg=self.colors['bg_secondary'])
        btn_frame.pack(fill='x', padx=10, pady=10)

        tk.Button(btn_frame, text="🩺 HEAL NOW",
                 bg=self.colors['success'], fg=self.colors['text_primary'],
                 command=lambda: self.bot.character.perform_healing()).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🔮 CAST BUFFS",
                 bg=self.colors['accent_primary'], fg=self.colors['text_primary'],
                 command=self._cast_all_buffs).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🎯 FIND TARGET",
                 bg=self.colors['warning'], fg=self.colors['bg_primary'],
                 command=lambda: self.bot.combat.find_target()).pack(side='left', padx=5)

        tk.Button(btn_frame, text="🔧 REPAIR",
                 bg=self.colors['accent_secondary'], fg=self.colors['text_primary'],
                 command=lambda: self.bot.character.perform_repair()).pack(side='left', padx=5)