"""
Tantra Bot - Utilities Module
Common utility functions and helpers.
"""

import time
import logging
import ctypes
from ctypes import wintypes
from typing import Tuple, Optional, List
import win32gui
import win32con

def get_mouse_position() -> Tuple[int, int]:
    """Get current mouse cursor position."""
    point = wintypes.POINT()
    ctypes.windll.user32.GetCursorPos(ctypes.byref(point))
    return point.x, point.y

def get_window_under_cursor() -> Optional[int]:
    """Get window handle under mouse cursor."""
    x, y = get_mouse_position()
    return win32gui.WindowFromPoint((x, y))

def get_window_info(hwnd: int) -> dict:
    """Get information about a window."""
    try:
        rect = win32gui.GetWindowRect(hwnd)
        title = win32gui.GetWindowText(hwnd)
        class_name = win32gui.GetClassName(hwnd)

        return {
            'handle': hwnd,
            'title': title,
            'class_name': class_name,
            'rect': rect,
            'width': rect[2] - rect[0],
            'height': rect[3] - rect[1]
        }
    except:
        return {}

def find_windows_by_title(title_pattern: str) -> List[int]:
    """Find all windows matching title pattern."""
    windows = []

    def enum_handler(hwnd, ctx):
        if win32gui.IsWindowVisible(hwnd):
            window_title = win32gui.GetWindowText(hwnd)
            if title_pattern.lower() in window_title.lower():
                windows.append(hwnd)
        return True

    win32gui.EnumWindows(enum_handler, None)
    return windows

def format_time_duration(seconds: float) -> str:
    """Format time duration in human readable format."""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"

def format_bytes(bytes_value: int) -> str:
    """Format bytes in human readable format."""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if bytes_value < 1024.0:
            return f"{bytes_value:.1f} {unit}"
        bytes_value /= 1024.0
    return f"{bytes_value:.1f} TB"

def safe_int(value: str, default: int = 0) -> int:
    """Safely convert string to int."""
    try:
        return int(value)
    except (ValueError, TypeError):
        return default

def safe_float(value: str, default: float = 0.0) -> float:
    """Safely convert string to float."""
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def clamp(value: float, min_val: float, max_val: float) -> float:
    """Clamp value between min and max."""
    return max(min_val, min(value, max_val))

def hex_to_bytes(hex_string: str) -> bytes:
    """Convert hex string to bytes."""
    # Remove spaces and common prefixes
    hex_string = hex_string.replace(' ', '').replace('0x', '')

    # Ensure even length
    if len(hex_string) % 2:
        hex_string = '0' + hex_string

    try:
        return bytes.fromhex(hex_string)
    except ValueError:
        return b''

def bytes_to_hex(data: bytes) -> str:
    """Convert bytes to hex string."""
    return data.hex().upper()

class PerformanceTimer:
    """Simple performance timer for measuring execution time."""

    def __init__(self):
        self.start_time = None
        self.end_time = None

    def start(self):
        """Start the timer."""
        self.start_time = time.perf_counter()
        self.end_time = None

    def stop(self) -> float:
        """Stop the timer and return elapsed time."""
        if self.start_time is None:
            return 0.0

        self.end_time = time.perf_counter()
        return self.end_time - self.start_time

    def elapsed(self) -> float:
        """Get elapsed time without stopping."""
        if self.start_time is None:
            return 0.0

        current_time = time.perf_counter()
        return current_time - self.start_time

class RateLimiter:
    """Rate limiter to control execution frequency."""

    def __init__(self, max_calls: int, time_window: float):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []

    def can_execute(self) -> bool:
        """Check if execution is allowed."""
        current_time = time.time()

        # Remove old calls outside time window
        self.calls = [call_time for call_time in self.calls
                     if current_time - call_time < self.time_window]

        # Check if we can make another call
        return len(self.calls) < self.max_calls

    def execute(self) -> bool:
        """Record execution if allowed."""
        if self.can_execute():
            self.calls.append(time.time())
            return True
        return False

def setup_logger(name: str, log_file: str = None, level: int = logging.INFO) -> logging.Logger:
    """Setup a logger with file and console handlers."""
    logger = logging.getLogger(name)
    logger.setLevel(level)

    # Clear existing handlers
    logger.handlers.clear()

    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # File handler
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

    return logger

def validate_memory_address(address_str: str) -> Optional[int]:
    """Validate and convert memory address string to integer."""
    try:
        # Handle hex format
        if address_str.startswith('0x') or address_str.startswith('0X'):
            return int(address_str, 16)

        # Handle decimal format
        if address_str.isdigit():
            return int(address_str)

        # Handle hex without prefix
        try:
            return int(address_str, 16)
        except ValueError:
            return None

    except (ValueError, TypeError):
        return None

def create_memory_pattern(pattern_str: str) -> bytes:
    """Create memory search pattern from string."""
    # Handle different pattern formats
    if ' ' in pattern_str:
        # Space-separated hex bytes
        hex_parts = pattern_str.split()
        return bytes([int(part, 16) for part in hex_parts if part != '??'])
    else:
        # Continuous hex string
        return hex_to_bytes(pattern_str)

def is_process_running(process_name: str) -> bool:
    """Check if a process is currently running."""
    try:
        import psutil
        for proc in psutil.process_iter(['name']):
            if proc.info['name'] and process_name.lower() in proc.info['name'].lower():
                return True
        return False
    except ImportError:
        # Fallback method using Windows API
        import ctypes
        from ctypes import wintypes

        # This is a simplified check - would need more implementation
        return True  # Placeholder

def get_system_info() -> dict:
    """Get basic system information."""
    try:
        import platform
        import psutil

        return {
            'platform': platform.platform(),
            'processor': platform.processor(),
            'memory_total': psutil.virtual_memory().total,
            'memory_available': psutil.virtual_memory().available,
            'python_version': platform.python_version()
        }
    except ImportError:
        return {
            'platform': 'Unknown',
            'processor': 'Unknown',
            'memory_total': 0,
            'memory_available': 0,
            'python_version': 'Unknown'
        }