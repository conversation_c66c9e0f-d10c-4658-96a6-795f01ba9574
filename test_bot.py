#!/usr/bin/env python3
"""
Tantra Bot - Test Script
Tests all modules and functionality without requiring the game to be running.
"""

import sys
import logging
from typing import Dict, Any

def test_imports():
    """Test that all modules can be imported."""
    print("🔍 Testing module imports...")

    try:
        from memory_manager import MemoryManager
        print("✅ memory_manager imported successfully")

        from game_interface import GameInterface
        print("✅ game_interface imported successfully")

        from character_manager import CharacterManager
        print("✅ character_manager imported successfully")

        from combat_system import CombatSystem
        print("✅ combat_system imported successfully")

        from social_manager import SocialManager
        print("✅ social_manager imported successfully")

        from auto_login import AutoLogin
        print("✅ auto_login imported successfully")

        from portal_hack import PortalHack
        print("✅ portal_hack imported successfully")

        from config_manager import ConfigManager
        print("✅ config_manager imported successfully")

        from gui_interface import EpicGUI
        print("✅ gui_interface imported successfully")

        from utils import *
        print("✅ utils imported successfully")

        return True

    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_config_manager():
    """Test configuration manager functionality."""
    print("\n🔧 Testing configuration manager...")

    try:
        from config_manager import ConfigManager

        config = ConfigManager("test_config.json")

        # Test default config
        assert config.config is not None
        print("✅ Default configuration loaded")

        # Test save/load
        config.save_config()
        print("✅ Configuration saved")

        config.load_config()
        print("✅ Configuration loaded")

        # Test config updates
        config.update_config('character', {'heal_threshold': 80})
        assert config.get_config_value('character', 'heal_threshold') == 80
        print("✅ Configuration update works")

        # Test validation
        issues = config.validate_config()
        print(f"✅ Configuration validation: {len(issues['errors'])} errors, {len(issues['warnings'])} warnings")

        return True

    except Exception as e:
        print(f"❌ Config manager test failed: {e}")
        return False

def test_memory_manager():
    """Test memory manager (without actual process)."""
    print("\n🧠 Testing memory manager...")

    try:
        from memory_manager import MemoryManager

        memory = MemoryManager()
        print("✅ Memory manager created")

        # Test without process (should fail gracefully)
        result = memory.read_memory(0x1000, 4)
        assert result is None  # Should fail without process
        print("✅ Memory read fails gracefully without process")

        return True

    except Exception as e:
        print(f"❌ Memory manager test failed: {e}")
        return False

def test_utilities():
    """Test utility functions."""
    print("\n🛠️ Testing utilities...")

    try:
        from utils import *

        # Test safe conversions
        assert safe_int("123") == 123
        assert safe_int("invalid", 42) == 42
        print("✅ Safe int conversion works")

        assert safe_float("123.45") == 123.45
        assert safe_float("invalid", 3.14) == 3.14
        print("✅ Safe float conversion works")

        # Test hex conversion
        hex_data = hex_to_bytes("48656C6C6F")
        assert hex_data == b"Hello"
        print("✅ Hex to bytes conversion works")

        # Test performance timer
        timer = PerformanceTimer()
        timer.start()
        import time
        time.sleep(0.01)
        elapsed = timer.stop()
        assert elapsed > 0
        print("✅ Performance timer works")

        # Test rate limiter
        limiter = RateLimiter(5, 1.0)
        for i in range(5):
            assert limiter.execute() == True
        assert limiter.execute() == False  # Should be rate limited
        print("✅ Rate limiter works")

        return True

    except Exception as e:
        print(f"❌ Utilities test failed: {e}")
        return False

def test_module_creation():
    """Test creating module instances."""
    print("\n🏗️ Testing module creation...")

    try:
        from memory_manager import MemoryManager
        from game_interface import GameInterface
        from character_manager import CharacterManager
        from combat_system import CombatSystem
        from social_manager import SocialManager
        from auto_login import AutoLogin
        from portal_hack import PortalHack

        # Create core modules
        memory = MemoryManager()
        game = GameInterface()
        character = CharacterManager(memory, game)
        combat = CombatSystem(memory, game, character)
        social = SocialManager(memory, game)
        auto_login = AutoLogin(game)
        portal_hack = PortalHack(memory, game)

        print("✅ All modules created successfully")

        # Test configuration updates
        test_config = {'heal_threshold': 75}
        character.update_config(test_config)
        print("✅ Character config update works")

        combat.update_config({'enable_skills': False})
        print("✅ Combat config update works")

        social.update_config({'enable_pm_reply': True})
        print("✅ Social config update works")

        return True

    except Exception as e:
        print(f"❌ Module creation test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🔥 TANTRA BOT - EPIC EDITION - TEST SUITE 🔥")
    print("=" * 50)

    tests = [
        ("Module Imports", test_imports),
        ("Configuration Manager", test_config_manager),
        ("Memory Manager", test_memory_manager),
        ("Utilities", test_utilities),
        ("Module Creation", test_module_creation)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")

    print("\n" + "=" * 50)
    print(f"🎯 TEST RESULTS: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 ALL TESTS PASSED! Bot is ready to use!")
        print("\n🚀 To start the bot:")
        print("   1. Run as Administrator")
        print("   2. Start Tantra Online")
        print("   3. Run: python tantra_bot.py")
        print("   4. Configure settings in GUI")
        print("   5. Press F8 to start automation")
    else:
        print("⚠️ Some tests failed. Check the errors above.")
        print("Make sure all dependencies are installed:")
        print("   pip install -r requirements.txt")

    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)