"""
Tantra Bot - Combat System Module
Handles target finding, skill rotation, anti-stuck mechanics, and combat logic.
"""

import time
import logging
from typing import Optional, List, Dict, Any
from memory_manager import MemoryManager
from game_interface import GameInterface
from character_manager import CharacterManager

class CombatSystem:
    """Manages combat operations including targeting, skills, and anti-stuck."""

    def __init__(self, memory_manager: MemoryManager, game_interface: GameInterface,
                 character_manager: CharacterManager):
        self.memory = memory_manager
        self.game = game_interface
        self.character = character_manager
        self.logger = logging.getLogger(__name__)

        # Memory addresses
        self.base_address_target_monster = 0x109BE280

        # Combat state
        self.current_target = None
        self.last_damage = 0
        self.stuck_counter = 0
        self.skill_rotation_index = 0

        # Configuration
        self.config = {
            'target_monsters': [
                'ANTI AVARA CARA',
                '<PERSON><PERSON><PERSON>',
                '<PERSON><PERSON>'
            ],
            'enable_monster_select': True,
            'enable_skills': True,
            'enable_r_key': True,  # Auto-attack
            'enable_looting': True,
            'skill_delays': {
                '1': 200,
                '2': 200,
                '3': 200,
                '4': 200
            },
            'loot_delay': 200,
            'anti_stuck_spin_time': 150,
            'anti_stuck_move_time': 500,
            'delay_before_skill': 1000,
            'enable_delay_before_skill': True
        }

        # Skill rotation
        self.skill_rotation = ['1', '2', '4', '3']  # F1 skills 1,2,4,3

    def get_target_monster_name(self) -> Optional[str]:
        """Get the name of the currently targeted monster."""
        try:
            # Get pointer to monster name
            pointer_address = self.memory.get_pointer_value(self.base_address_target_monster)
            if not pointer_address:
                return None

            # Read monster name
            monster_name = self.memory.read_string(pointer_address, 64)
            return monster_name.strip() if monster_name else None

        except Exception as e:
            self.logger.error(f"Error reading target monster name: {e}")
            return None

    def is_valid_target(self, monster_name: str) -> bool:
        """Check if the monster is in our target list."""
        if not self.config['enable_monster_select']:
            return True

        if not monster_name:
            return False

        # Check if monster name contains any of our target strings
        for target in self.config['target_monsters']:
            if target.upper() in monster_name.upper():
                return True

        return False

    def find_target(self) -> bool:
        """Find and target a valid monster."""
        try:
            if not self.config['enable_monster_select'] and not self.config['enable_skills']:
                return True

            # Update character status first
            self.character.update_character_status()

            # Don't target if dead
            if self.character.is_dead:
                self.character.auto_resurrect()
                return False

            max_attempts = 10
            attempts = 0

            while attempts < max_attempts:
                # Send target enemy command
                if self.config['enable_r_key']:
                    self.game.send_key('t')
                    self.game.send_key('e')
                elif self.config['enable_skills']:
                    self.game.send_key('t')
                    self.game.send_key('e')

                time.sleep(0.5)

                # Get current target
                target_name = self.get_target_monster_name()

                if self.is_valid_target(target_name):
                    self.current_target = target_name
                    self.logger.debug(f"Valid target found: {target_name}")

                    # Send R key if enabled
                    if self.config['enable_r_key']:
                        self.game.send_key('r')

                    # Delay before skill if enabled
                    if self.config['enable_delay_before_skill']:
                        time.sleep(self.config['delay_before_skill'] / 1000.0)

                    return True
                else:
                    # No valid target, perform anti-stuck movement
                    if self.config['enable_r_key'] or self.config['enable_skills']:
                        self.perform_anti_stuck_movement()

                attempts += 1

            return False

        except Exception as e:
            self.logger.error(f"Error finding target: {e}")
            return False

    def perform_anti_stuck_movement(self):
        """Perform anti-stuck movement (spin and move)."""
        try:
            # Click to unfocus chat
            self.game.unfocus_chat()

            # Spin (A key)
            self.game.send_key('a', self.config['anti_stuck_spin_time'])

            # Move forward (W key)
            self.game.send_key('w', self.config['anti_stuck_move_time'])

        except Exception as e:
            self.logger.error(f"Error during anti-stuck movement: {e}")

    def use_skill(self, skill_key: str) -> bool:
        """Use a specific skill."""
        if not self.config['enable_skills']:
            return False

        try:
            # Ensure we have a valid target first
            if not self.find_target():
                return False

            # Use TP potion if needed
            self.character.use_tp_potion()

            # Send skill key
            self.game.send_key(skill_key, self.config['skill_delays'].get(skill_key, 200))

            return True

        except Exception as e:
            self.logger.error(f"Error using skill {skill_key}: {e}")
            return False

    def execute_skill_rotation(self) -> bool:
        """Execute the next skill in rotation."""
        if not self.config['enable_skills']:
            return False

        try:
            # Get next skill in rotation
            skill_key = self.skill_rotation[self.skill_rotation_index]

            # Use the skill
            success = self.use_skill(skill_key)

            # Advance rotation index
            self.skill_rotation_index = (self.skill_rotation_index + 1) % len(self.skill_rotation)

            return success

        except Exception as e:
            self.logger.error(f"Error executing skill rotation: {e}")
            return False

    def perform_looting(self) -> bool:
        """Perform looting action."""
        if not self.config['enable_looting']:
            return False

        try:
            # Send loot key (F)
            self.game.send_key('f', self.config['loot_delay'])
            return True

        except Exception as e:
            self.logger.error(f"Error performing looting: {e}")
            return False

    def check_anti_stuck(self) -> bool:
        """Check if character is stuck and perform anti-stuck if needed."""
        try:
            # Get current damage value
            current_damage = self.character.current_damage

            # If damage hasn't changed, character might be stuck
            if current_damage == self.last_damage:
                self.stuck_counter += 1
            else:
                self.stuck_counter = 0
                self.last_damage = current_damage

            # If stuck for too long, perform anti-stuck movement
            if self.stuck_counter >= 3:
                self.logger.info("Character appears stuck, performing anti-stuck movement")
                self.perform_anti_stuck_movement()
                self.stuck_counter = 0
                return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking anti-stuck: {e}")
            return False

    def combat_cycle(self) -> bool:
        """Execute one complete combat cycle."""
        try:
            # Update character status
            self.character.update_character_status()

            # Handle death
            if self.character.is_dead:
                return self.character.auto_resurrect()

            # Perform character maintenance
            self.character.perform_maintenance()

            # Find target
            if not self.find_target():
                return False

            # Execute skill
            skill_used = self.execute_skill_rotation()

            # Perform looting
            self.perform_looting()

            # Check for stuck condition
            self.check_anti_stuck()

            return skill_used

        except Exception as e:
            self.logger.error(f"Error during combat cycle: {e}")
            return False

    def update_config(self, new_config: Dict[str, Any]):
        """Update combat configuration."""
        self.config.update(new_config)
        self.logger.info("Combat system configuration updated")

    def add_target_monster(self, monster_name: str):
        """Add a monster to the target list."""
        if monster_name not in self.config['target_monsters']:
            self.config['target_monsters'].append(monster_name)
            self.logger.info(f"Added target monster: {monster_name}")

    def remove_target_monster(self, monster_name: str):
        """Remove a monster from the target list."""
        if monster_name in self.config['target_monsters']:
            self.config['target_monsters'].remove(monster_name)
            self.logger.info(f"Removed target monster: {monster_name}")

    def clear_target_monsters(self):
        """Clear all target monsters."""
        self.config['target_monsters'].clear()
        self.logger.info("Cleared all target monsters")

    def get_combat_status(self) -> Dict[str, Any]:
        """Get current combat status."""
        return {
            'current_target': self.current_target,
            'skill_rotation_index': self.skill_rotation_index,
            'next_skill': self.skill_rotation[self.skill_rotation_index],
            'stuck_counter': self.stuck_counter,
            'target_monsters': self.config['target_monsters'],
            'enable_skills': self.config['enable_skills'],
            'enable_monster_select': self.config['enable_monster_select'],
            'enable_looting': self.config['enable_looting']
        }